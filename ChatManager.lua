local addonName, addon = ...
addon.L = addon.L or {}
local L = addon.L

-- 保存设置的数据表
ChatManagerDB = ChatManagerDB or {}

-- 默认配置
local defaults = {
    channelOrder = {
        ["综合"] = 1,
        ["交易"] = 2,
        ["本地防务"] = 3,
        ["大脚世界频道"] = 4
    },
    channelNames = {
        ["大脚世界频道"] = "世界"
    },
    autoJoinEnabled = true,
    autoJoinChannels = {"大脚世界频道"},
    filters = {},
    minimapButton = { hide = false },
    timestamp = {
        enabled = false,
        format = "[%H:%M:%S]",
        formats = {
            "无",
            "[%H:%M]",
            "[%I:%M %p]",
            "[%H:%M:%S]",
            "[%I:%M:%S %p]",
            "[%M:%S]"
        }
    },
    -- 添加玩家和宠物头像设置
    combatText = {
        hidePlayerFrame = false,
        hidePetFrame = true
    }
}

-- 序列化和反序列化函数
local function Serialize(data)
    local serialized = ""
    local dataType = type(data)
    
    if dataType == "table" then
        serialized = serialized .. "t{"
        for k, v in pairs(data) do
            serialized = serialized .. "[" .. Serialize(k) .. "]=" .. Serialize(v) .. ","
        end
        serialized = serialized .. "}"
    elseif dataType == "string" then
        return string.format("%q", data)
    elseif dataType == "number" or dataType == "boolean" then
        return tostring(data)
    end
    
    return serialized
end

local function Deserialize(str)
    if str == "" then return nil end
    local fn = loadstring("return " .. str)
    if fn then
        return pcall(fn)
    end
    return false, "Invalid serialized data"
end

-- 初始化配置
for key, value in pairs(defaults) do
    if ChatManagerDB[key] == nil then
        ChatManagerDB[key] = value
    end
end

-- 更新频道排序和名称
local function UpdateChannels()
    local success, channels = pcall(GetChannelList)
    if not success then
        print("聊天频道管理：获取频道列表失败")
        return
    end

    -- 创建频道ID到名称的映射
    local channelMap = {}
    for i = 1, #channels, 3 do
        local id = channels[i]
        local name = channels[i + 1]
        local disabled = channels[i + 2]

        if not disabled then
            channelMap[name] = id
            -- 检查是否有自定义名称
            local customName = ChatManagerDB.channelNames[name]
            if customName then
                pcall(SetChannelName, id, customName)
            end
        end
    end

    -- 优化的频道排序
    local order = ChatManagerDB.channelOrder
    local sortedChannels = {}

    -- 收集需要排序的频道
    for name, targetPos in pairs(order) do
        if channelMap[name] then
            table.insert(sortedChannels, {id = channelMap[name], pos = targetPos})
        end
    end

    -- 按目标位置排序
    table.sort(sortedChannels, function(a, b) return a.pos < b.pos end)

    -- 执行排序
    for _, channel in ipairs(sortedChannels) do
        pcall(SwapChannelsByChannelIndex, channel.id, channel.pos)
    end
end

-- 初始化函数
local function Initialize()
    -- 创建设置面板
    local frame = CreateFrame("Frame")
    frame:RegisterEvent("PLAYER_LOGIN")
    frame:RegisterEvent("CHANNEL_UI_UPDATE")
    frame:RegisterEvent("CHAT_MSG_CHANNEL")
    frame:RegisterEvent("CHAT_MSG_SAY")
    frame:RegisterEvent("CHAT_MSG_YELL")
    frame:RegisterEvent("CHAT_MSG_WHISPER")
    frame:RegisterEvent("CHAT_MSG_PARTY")
    frame:RegisterEvent("CHAT_MSG_RAID")
    frame:RegisterEvent("CHAT_MSG_GUILD")

    frame:SetScript("OnEvent", function(self, event, ...)
        if event == "PLAYER_LOGIN" then
            -- 自动加入频道
            if ChatManagerDB.autoJoinEnabled then
                C_Timer.After(5, function()
                    for _, channelName in ipairs(ChatManagerDB.autoJoinChannels) do
                        JoinChannelByName(channelName)
                    end
                end)
            end
        elseif event == "CHANNEL_UI_UPDATE" then
            -- 更新频道排序和名称
            UpdateChannels()
        else
            -- 处理聊天消息的时间戳
            if ChatManagerDB.timestamp.enabled then
                local timestamp = date(ChatManagerDB.timestamp.format)
                local msg = select(1, ...)
                if msg then
                    msg = timestamp .. " " .. msg
                    return false, msg
                end
            end
        end
    end)
end

-- 将UpdateChannels函数存储到addon表中，供其他函数使用
addon.UpdateChannels = UpdateChannels

-- 设置频道自定义名称
function L:SetChannelCustomName(originalName, customName)
    ChatManagerDB.channelNames[originalName] = customName
    addon.UpdateChannels()
end

-- 设置频道顺序
function L:SetChannelOrder(channelName, order)
    ChatManagerDB.channelOrder[channelName] = order
    addon.UpdateChannels()
end

-- 设置是否自动加入频道
function L:SetAutoJoinEnabled(enabled)
    ChatManagerDB.autoJoinEnabled = enabled
end

-- 添加自动加入的频道
function L:AddAutoJoinChannel(channelName)
    if not tContains(ChatManagerDB.autoJoinChannels, channelName) then
        table.insert(ChatManagerDB.autoJoinChannels, channelName)
    end
end

-- 移除自动加入的频道
function L:RemoveAutoJoinChannel(channelName)
    for i = #ChatManagerDB.autoJoinChannels, 1, -1 do
        if ChatManagerDB.autoJoinChannels[i] == channelName then
            table.remove(ChatManagerDB.autoJoinChannels, i)
            break
        end
    end
end

-- 初始化插件
Initialize()

-- 应用玩家和宠物头像伤害数字设置
local function ApplyCombatTextSettings()
    if ChatManagerDB.combatText.hidePlayerFrame then
        PlayerFrame:UnregisterEvent("UNIT_COMBAT")
    else
        PlayerFrame:RegisterEvent("UNIT_COMBAT")
    end
    
    if ChatManagerDB.combatText.hidePetFrame then
        PetFrame:UnregisterEvent("UNIT_COMBAT")
    else
        PetFrame:RegisterEvent("UNIT_COMBAT")
    end
end

-- 应用设置
ApplyCombatTextSettings()

-- 导出给其他文件使用
_G[addonName] = addon