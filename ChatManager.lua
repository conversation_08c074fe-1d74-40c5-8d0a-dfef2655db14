local addonName, addon = ...
addon.L = addon.L or {}
local L = addon.L

-- 版本信息
addon.version = "2.0.0"
addon.author = "SimonChen"

-- 保存设置的数据表
ChatManagerDB = ChatManagerDB or {}

-- 默认配置 - 重构为更清晰的结构
local defaults = {
    -- 版本信息
    version = addon.version,

    -- 频道管理
    channels = {
        autoJoinEnabled = true,
        autoJoinChannels = {"大脚世界频道", "综合", "交易", "本地防务"},
        autoSortEnabled = true,
        channelOrder = {
            ["综合"] = 1,
            ["交易"] = 2,
            ["本地防务"] = 3,
            ["大脚世界频道"] = 4
        },
        channelNames = {
            ["大脚世界频道"] = "世界"
        },
        joinDelay = 5 -- 自动加入延迟（秒）
    },

    -- 聊天功能
    chat = {
        timestamp = {
            enabled = false,
            format = "[%H:%M:%S]",
            color = {1, 1, 1, 0.8},
            formats = {
                "无",
                "[%H:%M]",
                "[%I:%M %p]",
                "[%H:%M:%S]",
                "[%I:%M:%S %p]",
                "[%M:%S]"
            }
        },
        filters = {
            enabled = false,
            keywords = {},
            blockedWords = {},
            highlightKeywords = {}
        },
        history = {
            enabled = true,
            maxLines = 1000
        }
    },

    -- 界面设置
    ui = {
        minimapButton = {
            hide = false,
            position = 45
        },
        combatText = {
            hidePlayerFrame = false,
            hidePetFrame = true
        },
        actionButtons = {
            enabled = true,
            fontSize = 14,
            fontOutline = true
        }
    },

    -- 高级设置
    advanced = {
        debugMode = false,
        autoBackup = true,
        backupInterval = 7 -- 天
    }
}

-- 配置管理工具
local Config = {}
addon.Config = Config

-- 深度复制表
local function DeepCopy(orig)
    local copy
    if type(orig) == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[DeepCopy(orig_key)] = DeepCopy(orig_value)
        end
        setmetatable(copy, DeepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- 深度合并表
local function DeepMerge(target, source)
    for key, value in pairs(source) do
        if type(value) == "table" and type(target[key]) == "table" then
            DeepMerge(target[key], value)
        elseif target[key] == nil then
            target[key] = DeepCopy(value)
        end
    end
    return target
end

-- 获取配置值
function Config:Get(path, defaultValue)
    local keys = {strsplit(".", path)}
    local current = ChatManagerDB

    for _, key in ipairs(keys) do
        if type(current) ~= "table" or current[key] == nil then
            return defaultValue
        end
        current = current[key]
    end

    return current
end

-- 设置配置值
function Config:Set(path, value)
    local keys = {strsplit(".", path)}
    local current = ChatManagerDB

    for i = 1, #keys - 1 do
        local key = keys[i]
        if type(current[key]) ~= "table" then
            current[key] = {}
        end
        current = current[key]
    end

    current[keys[#keys]] = value

    -- 触发配置更改事件
    addon:FireEvent("CONFIG_CHANGED", path, value)
end

-- 重置配置
function Config:Reset()
    ChatManagerDB = DeepCopy(defaults)
    addon:FireEvent("CONFIG_RESET")
end

-- 导出配置
function Config:Export()
    local exportData = {
        version = addon.version,
        timestamp = time(),
        config = DeepCopy(ChatManagerDB)
    }
    return addon:Serialize(exportData)
end

-- 导入配置
function Config:Import(data)
    local success, importData = addon:Deserialize(data)
    if not success then
        return false, "无效的配置数据"
    end

    if not importData.config then
        return false, "配置数据格式错误"
    end

    -- 备份当前配置
    local backup = DeepCopy(ChatManagerDB)

    -- 导入新配置
    ChatManagerDB = DeepCopy(importData.config)
    DeepMerge(ChatManagerDB, defaults) -- 确保所有默认值存在

    addon:FireEvent("CONFIG_IMPORTED", backup)
    return true, "配置导入成功"
end

-- 事件系统
local eventCallbacks = {}

function addon:RegisterEvent(event, callback)
    if not eventCallbacks[event] then
        eventCallbacks[event] = {}
    end
    table.insert(eventCallbacks[event], callback)
end

function addon:FireEvent(event, ...)
    if eventCallbacks[event] then
        for _, callback in ipairs(eventCallbacks[event]) do
            callback(...)
        end
    end
end

-- 序列化工具
function addon:Serialize(data)
    local function serializeValue(val)
        local t = type(val)
        if t == "string" then
            return string.format("%q", val)
        elseif t == "number" or t == "boolean" then
            return tostring(val)
        elseif t == "table" then
            local parts = {}
            for k, v in pairs(val) do
                table.insert(parts, "[" .. serializeValue(k) .. "]=" .. serializeValue(v))
            end
            return "{" .. table.concat(parts, ",") .. "}"
        else
            return "nil"
        end
    end
    return serializeValue(data)
end

function addon:Deserialize(str)
    if not str or str == "" then
        return false, "空数据"
    end

    local func, err = loadstring("return " .. str)
    if not func then
        return false, "语法错误: " .. (err or "未知错误")
    end

    local success, result = pcall(func)
    if not success then
        return false, "执行错误: " .. (result or "未知错误")
    end

    return true, result
end

-- 初始化配置
DeepMerge(ChatManagerDB, defaults)

-- 版本升级处理
if not ChatManagerDB.version or ChatManagerDB.version ~= addon.version then
    addon:FireEvent("VERSION_UPGRADE", ChatManagerDB.version, addon.version)
    ChatManagerDB.version = addon.version
end

-- 频道管理模块
local ChannelManager = {}
addon.ChannelManager = ChannelManager

-- 获取当前频道列表
function ChannelManager:GetChannelList()
    local success, channels = pcall(GetChannelList)
    if not success then
        if addon.Config:Get("advanced.debugMode") then
            print("|cffff0000[ChatManager]|r 获取频道列表失败")
        end
        return {}
    end

    local channelMap = {}
    for i = 1, #channels, 3 do
        local id = channels[i]
        local name = channels[i + 1]
        local disabled = channels[i + 2]

        if not disabled then
            channelMap[name] = {
                id = id,
                name = name,
                position = math.ceil(i / 3)
            }
        end
    end

    return channelMap
end

-- 自动加入频道
function ChannelManager:AutoJoinChannels()
    if not addon.Config:Get("channels.autoJoinEnabled") then
        return
    end

    local channels = addon.Config:Get("channels.autoJoinChannels", {})
    local delay = addon.Config:Get("channels.joinDelay", 5)

    C_Timer.After(delay, function()
        for _, channelName in ipairs(channels) do
            local success = pcall(JoinChannelByName, channelName)
            if addon.Config:Get("advanced.debugMode") and success then
                print("|cff00ff00[ChatManager]|r 已加入频道: " .. channelName)
            end
        end

        -- 加入后自动排序
        if addon.Config:Get("channels.autoSortEnabled") then
            C_Timer.After(2, function()
                self:SortChannels()
            end)
        end
    end)
end

-- 频道排序
function ChannelManager:SortChannels()
    local channelMap = self:GetChannelList()
    local order = addon.Config:Get("channels.channelOrder", {})
    local sortedChannels = {}

    -- 收集需要排序的频道
    for name, targetPos in pairs(order) do
        if channelMap[name] then
            table.insert(sortedChannels, {
                id = channelMap[name].id,
                name = name,
                targetPos = targetPos,
                currentPos = channelMap[name].position
            })
        end
    end

    -- 按目标位置排序
    table.sort(sortedChannels, function(a, b) return a.targetPos < b.targetPos end)

    -- 执行排序
    for _, channel in ipairs(sortedChannels) do
        if channel.currentPos ~= channel.targetPos then
            local success = pcall(SwapChannelsByChannelIndex, channel.id, channel.targetPos)
            if addon.Config:Get("advanced.debugMode") and success then
                print("|cff00ff00[ChatManager]|r 频道排序: " .. channel.name .. " -> 位置 " .. channel.targetPos)
            end
        end
    end
end

-- 应用频道自定义名称
function ChannelManager:ApplyCustomNames()
    local channelMap = self:GetChannelList()
    local customNames = addon.Config:Get("channels.channelNames", {})

    for originalName, customName in pairs(customNames) do
        if channelMap[originalName] then
            pcall(SetChannelName, channelMap[originalName].id, customName)
        end
    end
end

-- 更新频道（综合方法）
function ChannelManager:UpdateChannels()
    self:ApplyCustomNames()
    if addon.Config:Get("channels.autoSortEnabled") then
        self:SortChannels()
    end
end

-- 聊天功能模块
local ChatFeatures = {}
addon.ChatFeatures = ChatFeatures

-- 时间戳功能
function ChatFeatures:ApplyTimestamp(message)
    if not addon.Config:Get("chat.timestamp.enabled") then
        return message
    end

    local format = addon.Config:Get("chat.timestamp.format", "[%H:%M:%S]")
    local color = addon.Config:Get("chat.timestamp.color", {1, 1, 1, 0.8})
    local timestamp = date(format)

    local colorCode = string.format("|cff%02x%02x%02x",
        math.floor(color[1] * 255),
        math.floor(color[2] * 255),
        math.floor(color[3] * 255)
    )

    return colorCode .. timestamp .. "|r " .. message
end

-- 关键词高亮
function ChatFeatures:HighlightKeywords(message)
    local keywords = addon.Config:Get("chat.filters.highlightKeywords", {})
    if #keywords == 0 then
        return message
    end

    for _, keyword in ipairs(keywords) do
        if keyword ~= "" then
            message = message:gsub("(" .. keyword .. ")", "|cffff00ff%1|r")
        end
    end

    return message
end

-- 消息过滤
function ChatFeatures:FilterMessage(message)
    local blockedWords = addon.Config:Get("chat.filters.blockedWords", {})

    for _, word in ipairs(blockedWords) do
        if word ~= "" and message:lower():find(word:lower()) then
            return true -- 过滤掉这条消息
        end
    end

    return false
end

-- 主事件处理框架
local eventFrame = CreateFrame("Frame")
addon.eventFrame = eventFrame

-- 注册所有需要的事件
local events = {
    "PLAYER_LOGIN",
    "PLAYER_ENTERING_WORLD",
    "CHANNEL_UI_UPDATE",
    "CHAT_MSG_CHANNEL",
    "CHAT_MSG_SAY",
    "CHAT_MSG_YELL",
    "CHAT_MSG_WHISPER",
    "CHAT_MSG_PARTY",
    "CHAT_MSG_RAID",
    "CHAT_MSG_GUILD",
    "ADDON_LOADED"
}

for _, event in ipairs(events) do
    eventFrame:RegisterEvent(event)
end

-- 事件处理函数
eventFrame:SetScript("OnEvent", function(self, event, ...)
    if event == "ADDON_LOADED" then
        local loadedAddon = ...
        if loadedAddon == addonName then
            addon:FireEvent("ADDON_READY")
        end
    elseif event == "PLAYER_LOGIN" then
        addon:FireEvent("PLAYER_LOGIN")
    elseif event == "PLAYER_ENTERING_WORLD" then
        addon:FireEvent("PLAYER_ENTERING_WORLD")
    elseif event == "CHANNEL_UI_UPDATE" then
        addon.ChannelManager:UpdateChannels()
    else
        -- 处理聊天消息
        local message = select(1, ...)
        if message then
            -- 应用过滤器
            if addon.ChatFeatures:FilterMessage(message) then
                return
            end

            -- 应用时间戳和高亮
            message = addon.ChatFeatures:ApplyTimestamp(message)
            message = addon.ChatFeatures:HighlightKeywords(message)

            addon:FireEvent("CHAT_MESSAGE_PROCESSED", event, message, ...)
        end
    end
end)

-- 公共API
function L:SetChannelCustomName(originalName, customName)
    addon.Config:Set("channels.channelNames." .. originalName, customName)
    addon.ChannelManager:UpdateChannels()
end

function L:SetChannelOrder(channelName, order)
    addon.Config:Set("channels.channelOrder." .. channelName, order)
    addon.ChannelManager:UpdateChannels()
end

function L:SetAutoJoinEnabled(enabled)
    addon.Config:Set("channels.autoJoinEnabled", enabled)
end

function L:AddAutoJoinChannel(channelName)
    local channels = addon.Config:Get("channels.autoJoinChannels", {})
    if not tContains(channels, channelName) then
        table.insert(channels, channelName)
        addon.Config:Set("channels.autoJoinChannels", channels)
    end
end

function L:RemoveAutoJoinChannel(channelName)
    local channels = addon.Config:Get("channels.autoJoinChannels", {})
    for i = #channels, 1, -1 do
        if channels[i] == channelName then
            table.remove(channels, i)
            break
        end
    end
    addon.Config:Set("channels.autoJoinChannels", channels)
end

-- 事件监听器设置
addon:RegisterEvent("ADDON_READY", function()
    print("|cff00ff00[ChatManager]|r 聊天频道管理器已加载 v" .. addon.version)

    -- 创建小地图按钮
    if not addon.Config:Get("ui.minimapButton.hide") then
        addon:CreateMinimapButton()
    end
end)

addon:RegisterEvent("PLAYER_LOGIN", function()
    -- 自动加入频道
    addon.ChannelManager:AutoJoinChannels()
end)

addon:RegisterEvent("PLAYER_ENTERING_WORLD", function()
    -- 应用所有设置
    addon:ApplyAllSettings()
end)

-- 应用所有设置
function addon:ApplyAllSettings()
    self:ApplyCombatTextSettings()
    self:ApplyActionButtonSettings()
    self.ChannelManager:UpdateChannels()
end

-- 应用战斗文字设置
function addon:ApplyCombatTextSettings()
    local hidePlayer = self.Config:Get("ui.combatText.hidePlayerFrame", false)
    local hidePet = self.Config:Get("ui.combatText.hidePetFrame", true)

    if hidePlayer then
        PlayerFrame:UnregisterEvent("UNIT_COMBAT")
    else
        PlayerFrame:RegisterEvent("UNIT_COMBAT")
    end

    if hidePet then
        PetFrame:UnregisterEvent("UNIT_COMBAT")
    else
        PetFrame:RegisterEvent("UNIT_COMBAT")
    end
end

-- 应用动作条按钮设置
function addon:ApplyActionButtonSettings()
    if self.Config:Get("ui.actionButtons.enabled", true) then
        -- 这里会调用ActionButtonFonts.lua中的功能
        if self.ActionButtons then
            self.ActionButtons:UpdateAllButtons()
        end
    end
end

-- 创建小地图按钮
function addon:CreateMinimapButton()
    if self.minimapButton then
        return
    end

    local button = CreateFrame("Button", "ChatManagerMinimapButton", Minimap)
    button:SetSize(32, 32)
    button:SetFrameStrata("MEDIUM")
    button:SetFrameLevel(8)

    -- 设置图标
    local icon = button:CreateTexture(nil, "BACKGROUND")
    icon:SetSize(20, 20)
    icon:SetPoint("CENTER", 0, 0)
    icon:SetTexture("Interface\\ChatFrame\\UI-ChatIcon-Chat")

    -- 设置边框
    local border = button:CreateTexture(nil, "OVERLAY")
    border:SetSize(52, 52)
    border:SetPoint("TOPLEFT", 0, 0)
    border:SetTexture("Interface\\Minimap\\MiniMap-TrackingBorder")

    -- 鼠标交互
    button:SetScript("OnClick", function(self, btn)
        if btn == "LeftButton" then
            addon:ToggleSettingsPanel()
        elseif btn == "RightButton" then
            addon:ShowQuickMenu()
        end
    end)

    button:SetScript("OnEnter", function(self)
        GameTooltip:SetOwner(self, "ANCHOR_LEFT")
        GameTooltip:SetText("聊天频道管理器")
        GameTooltip:AddLine("左键: 打开设置", 1, 1, 1)
        GameTooltip:AddLine("右键: 快速菜单", 1, 1, 1)
        GameTooltip:Show()
    end)

    button:SetScript("OnLeave", function()
        GameTooltip:Hide()
    end)

    -- 位置设置
    local angle = math.rad(self.Config:Get("ui.minimapButton.position", 45))
    local x = math.cos(angle) * 80
    local y = math.sin(angle) * 80
    button:SetPoint("CENTER", Minimap, "CENTER", x, y)

    self.minimapButton = button
end

-- 切换设置面板
function addon:ToggleSettingsPanel()
    if self.settingsPanel and self.settingsPanel:IsShown() then
        self.settingsPanel:Hide()
    else
        self:ShowSettingsPanel()
    end
end

-- 显示快速菜单
function addon:ShowQuickMenu()
    -- 这里可以添加快速菜单功能
    print("快速菜单功能待实现")
end

-- 显示设置面板
function addon:ShowSettingsPanel()
    if not self.settingsPanel then
        self:CreateSettingsPanel()
    end
    self.settingsPanel:Show()
end

-- 导出给其他文件使用
_G[addonName] = addon

-- 立即应用设置
addon:ApplyAllSettings()