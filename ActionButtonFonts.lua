--[[ 动作条按钮样式修改 ]]--

-- 按键文本格式化函数（仅处理修饰键和鼠标按键）
local function FormatKeyText(text)
    if not text or type(text) ~= "string" then
        return text
    end
    
    -- 按键映射表
    local keyReplacements = {
        -- 修饰键
        ["(s%-)"] = "S",   -- Shift
        ["(a%-)"] = "A",   -- Alt
        ["(c%-)"] = "C",   -- Ctrl
        
        -- 鼠标按键
        [KEY_BUTTON1] = "LM",
        [KEY_BUTTON2] = "RM",
        [KEY_BUTTON3] = "M3",
        [KEY_BUTTON4] = "M4",
        [KEY_BUTTON5] = "M5",
        [KEY_MOUSEWHEELUP] = "MU",
        [KEY_MOUSEWHEELDOWN] = "MD",

        -- 小键盘按键
        [KEY_NUMPAD0] = "N0",
        [KEY_NUMPAD1] = "N1",
        [KEY_NUMPAD2] = "N2",
        [KEY_NUMPAD3] = "N3",
        [KEY_NUMPAD4] = "N4",
        [KEY_NUMPAD5] = "N5",
        [KEY_NUMPAD6] = "N6",
        [KEY_NUMPAD7] = "N7",
        [KEY_NUMPAD8] = "N8",
        [KEY_NUMPAD9] = "N9",
        [KEY_NUMPADDIVIDE] = "N/",
        [KEY_NUMPADMULTIPLY] = "N*",
        [KEY_NUMPADMINUS] = "N-",
        [KEY_NUMPADPLUS] = "N+",
        [KEY_NUMPADDECIMAL] = "N.",
        
        -- 中文版小键盘按键（添加字符串匹配）
        ["数字键盘0"] = "N0",
        ["数字键盘1"] = "N1",
        ["数字键盘2"] = "N2",
        ["数字键盘3"] = "N3",
        ["数字键盘4"] = "N4",
        ["数字键盘5"] = "N5",
        ["数字键盘6"] = "N6",
        ["数字键盘7"] = "N7",
        ["数字键盘8"] = "N8",
        ["数字键盘9"] = "N9",
        ["数字键盘/"] = "N/",
        ["数字键盘*"] = "N*",
        ["数字键盘-"] = "N-",
        ["数字键盘+"] = "N+",
        ["数字键盘."] = "N.",
    }

    -- 处理修饰键组合
    text = text:gsub("(s%-)(%u)", "S%2")  -- Shift+Key
              :gsub("(a%-)(%u)", "A%2")  -- Alt+Key
              :gsub("(c%-)(%u)", "C%2")  -- Ctrl+Key

    -- 基础替换
    for pattern, replacement in pairs(keyReplacements) do
        text = text:gsub(pattern, replacement)
    end

    return text
end

-- 核心修改函数：更新按钮的快捷键和层数样式
local function UpdateButtonText(button)
    -- 修改快捷键文字（右上角）
    local hotkey = button.HotKey
    if hotkey then
        -- 先获取原始文本并格式化
        local originalText = hotkey:GetText()
        
        -- 特殊处理数字键盘按键
        if originalText and originalText:find("数字键盘") then
            -- 直接处理中文数字键盘按键
            if originalText == "数字键盘-" then
                originalText = "N-"
            elseif originalText == "数字键盘+" then
                originalText = "N+"
            elseif originalText == "数字键盘/" then
                originalText = "N/"
            elseif originalText == "数字键盘*" then
                originalText = "N*"
            elseif originalText == "数字键盘." then
                originalText = "N."
            elseif originalText:find("数字键盘(%d)") then
                local num = originalText:match("数字键盘(%d)")
                if num then
                    originalText = "N" .. num
                end
            end
        end
        
        local formattedText = FormatKeyText(originalText)
        
        -- 应用样式修改
        hotkey:SetFontObject("GameFontHighlight")
        hotkey:SetTextColor(1, 1, 1, 1)
        hotkey:SetFont(hotkey:GetFont(), 14, "OUTLINE")
        hotkey:ClearAllPoints()
        hotkey:SetWidth(100)
        hotkey:SetPoint("TOPRIGHT", button, "TOPRIGHT", 0, -5)
        
        -- 设置格式化后的文本
        hotkey:SetText(formattedText)
    end

    -- 修改技能层数数字（右下角）
    local count = button.Count
    if count then
        count:SetFontObject("GameFontHighlight")
        count:SetTextColor(1, 1, 1, 1)
        count:SetFont(count:GetFont(), 14, "OUTLINE")
        count:ClearAllPoints()
        count:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", 0, 5)
    end
end

-- 所有需要修改的动作条前缀列表
local actionBars = {
    "ActionButton",          -- 主动作条1              
    "MultiBarBottomLeftButton",          -- 主动作条2 
    "MultiBarBottomRightButton",          -- 主动作条3
    "MultiBarRightButton",          -- 右侧竖排动作条1      
    "MultiBarLeftButton",          -- 右侧竖排动作条2       
    "MultiBar5Button",          -- 扩展动作条1          
    "MultiBar6Button"          -- 扩展动作条2           
}

-- 遍历并更新所有按钮
local function UpdateAllButtons()
    for _, barPrefix in ipairs(actionBars) do
        for i = 1, 12 do          -- 每个动作条最多12个按钮
            local button = _G[barPrefix .. i]
            if button and button.HotKey then
                UpdateButtonText(button)
            end
        end
    end
end

-- 事件监听：兼容延迟加载的按钮
local eventFrame = CreateFrame("Frame")
eventFrame:RegisterEvent("PLAYER_ENTERING_WORLD")
eventFrame:RegisterEvent("UPDATE_BINDINGS")          -- 新增绑定更新监听
eventFrame:SetScript("OnEvent", function()
    UpdateAllButtons()
end)

-- 立即执行一次初始化
UpdateAllButtons()