local addonName, addon = ...

-- 设置界面模块
local SettingsUI = {}
addon.SettingsUI = SettingsUI

-- 颜色常量
local COLORS = {
    BACKGROUND = {0.1, 0.1, 0.1, 0.9},
    HEADER = {0.2, 0.2, 0.2, 1},
    ACCENT = {0.3, 0.7, 1, 1},
    SUCCESS = {0, 1, 0, 1},
    WARNING = {1, 0.8, 0, 1},
    ERROR = {1, 0.2, 0.2, 1},
    TEXT = {0.9, 0.9, 0.9, 1}
}

-- 创建主设置面板
function addon:CreateSettingsPanel()
    if self.settingsPanel then
        return self.settingsPanel
    end
    
    -- 主框架
    local frame = CreateFrame("Frame", "ChatManagerSettingsFrame", UIParent, "BasicFrameTemplateWithInset")
    frame:SetSize(800, 600)
    frame:SetPoint("CENTER")
    frame:SetMovable(true)
    frame:EnableMouse(true)
    frame:RegisterForDrag("LeftButton")
    frame:SetScript("OnDragStart", frame.StartMoving)
    frame:SetScript("OnDragStop", frame.StopMovingOrSizing)
    frame:SetFrameStrata("DIALOG")
    
    -- 标题
    frame.title = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    frame.title:SetPoint("TOPLEFT", frame.TitleBg, "TOPLEFT", 5, -5)
    frame.title:SetText("聊天频道管理器 - 设置")
    
    -- 版本信息
    local version = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
    version:SetPoint("TOPRIGHT", frame.TitleBg, "TOPRIGHT", -5, -5)
    version:SetText("v" .. addon.version)
    version:SetTextColor(0.7, 0.7, 0.7, 1)
    
    -- 创建标签页系统
    self:CreateTabSystem(frame)
    
    -- 关闭按钮事件
    frame:SetScript("OnHide", function()
        -- 保存设置
        addon:FireEvent("SETTINGS_CLOSED")
    end)
    
    self.settingsPanel = frame
    return frame
end

-- 创建标签页系统
function addon:CreateTabSystem(parent)
    local tabContainer = CreateFrame("Frame", nil, parent)
    tabContainer:SetPoint("TOPLEFT", parent.Inset, "TOPLEFT", 10, -10)
    tabContainer:SetPoint("BOTTOMRIGHT", parent.Inset, "BOTTOMRIGHT", -10, 10)
    
    -- 标签页按钮容器
    local tabButtonContainer = CreateFrame("Frame", nil, tabContainer)
    tabButtonContainer:SetPoint("TOPLEFT", 0, 0)
    tabButtonContainer:SetPoint("TOPRIGHT", 0, 0)
    tabButtonContainer:SetHeight(30)
    
    -- 内容容器
    local contentContainer = CreateFrame("Frame", nil, tabContainer)
    contentContainer:SetPoint("TOPLEFT", tabButtonContainer, "BOTTOMLEFT", 0, -5)
    contentContainer:SetPoint("BOTTOMRIGHT", 0, 0)
    
    -- 标签页定义
    local tabs = {
        {
            name = "channels",
            title = "频道管理",
            icon = "Interface\\ChatFrame\\UI-ChatIcon-Chat",
            createFunc = function(container) return addon:CreateChannelTab(container) end
        },
        {
            name = "chat",
            title = "聊天功能",
            icon = "Interface\\ChatFrame\\UI-ChatIcon-Tell",
            createFunc = function(container) return addon:CreateChatTab(container) end
        },
        {
            name = "ui",
            title = "界面设置",
            icon = "Interface\\Buttons\\UI-GuildButton-PublicNote-Up",
            createFunc = function(container) return addon:CreateUITab(container) end
        },
        {
            name = "advanced",
            title = "高级设置",
            icon = "Interface\\Buttons\\UI-OptionsButton",
            createFunc = function(container) return addon:CreateAdvancedTab(container) end
        }
    }
    
    local tabButtons = {}
    local tabContents = {}
    local activeTab = nil
    
    -- 创建标签页按钮
    for i, tab in ipairs(tabs) do
        local button = CreateFrame("Button", nil, tabButtonContainer)
        button:SetSize(150, 25)
        button:SetPoint("TOPLEFT", (i-1) * 155, 0)
        
        -- 背景
        local bg = button:CreateTexture(nil, "BACKGROUND")
        bg:SetAllPoints()
        bg:SetColorTexture(0.2, 0.2, 0.2, 0.8)
        button.bg = bg
        
        -- 图标
        local icon = button:CreateTexture(nil, "ARTWORK")
        icon:SetSize(16, 16)
        icon:SetPoint("LEFT", 8, 0)
        icon:SetTexture(tab.icon)
        
        -- 文字
        local text = button:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        text:SetPoint("LEFT", icon, "RIGHT", 5, 0)
        text:SetText(tab.title)
        
        -- 点击事件
        button:SetScript("OnClick", function()
            addon:SwitchTab(tab.name, tabButtons, tabContents, contentContainer)
        end)
        
        -- 鼠标悬停
        button:SetScript("OnEnter", function()
            if activeTab ~= tab.name then
                bg:SetColorTexture(0.3, 0.3, 0.3, 0.8)
            end
        end)
        
        button:SetScript("OnLeave", function()
            if activeTab ~= tab.name then
                bg:SetColorTexture(0.2, 0.2, 0.2, 0.8)
            end
        end)
        
        tabButtons[tab.name] = button
        
        -- 创建标签页内容
        local content = CreateFrame("ScrollFrame", nil, contentContainer, "UIPanelScrollFrameTemplate")
        content:SetAllPoints()
        content:Hide()
        
        local scrollChild = CreateFrame("Frame", nil, content)
        scrollChild:SetSize(750, 1000)
        content:SetScrollChild(scrollChild)
        
        -- 调用创建函数
        tab.createFunc(scrollChild)
        
        tabContents[tab.name] = content
    end
    
    -- 默认显示第一个标签页
    addon:SwitchTab(tabs[1].name, tabButtons, tabContents, contentContainer)
    
    parent.tabButtons = tabButtons
    parent.tabContents = tabContents
    parent.activeTab = tabs[1].name
end

-- 切换标签页
function addon:SwitchTab(tabName, tabButtons, tabContents, contentContainer)
    -- 隐藏所有内容
    for name, content in pairs(tabContents) do
        content:Hide()
    end
    
    -- 重置所有按钮样式
    for name, button in pairs(tabButtons) do
        button.bg:SetColorTexture(0.2, 0.2, 0.2, 0.8)
    end
    
    -- 显示选中的内容
    if tabContents[tabName] then
        tabContents[tabName]:Show()
    end
    
    -- 高亮选中的按钮
    if tabButtons[tabName] then
        tabButtons[tabName].bg:SetColorTexture(unpack(COLORS.ACCENT))
    end
    
    contentContainer.activeTab = tabName
end

-- 工具函数：创建节
function SettingsUI:CreateSection(parent, title, yOffset)
    local section = CreateFrame("Frame", nil, parent)
    section:SetPoint("TOPLEFT", 10, yOffset or 0)
    section:SetPoint("TOPRIGHT", -10, yOffset or 0)
    section:SetHeight(200) -- 默认高度，会根据内容调整
    
    -- 背景
    local bg = section:CreateTexture(nil, "BACKGROUND")
    bg:SetAllPoints()
    bg:SetColorTexture(0.15, 0.15, 0.15, 0.5)
    
    -- 标题
    local titleText = section:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    titleText:SetPoint("TOPLEFT", 15, -10)
    titleText:SetText(title)
    titleText:SetTextColor(unpack(COLORS.ACCENT))
    
    -- 分隔线
    local line = section:CreateTexture(nil, "ARTWORK")
    line:SetPoint("TOPLEFT", titleText, "BOTTOMLEFT", 0, -5)
    line:SetPoint("TOPRIGHT", section, "TOPRIGHT", -15, -30)
    line:SetHeight(1)
    line:SetColorTexture(unpack(COLORS.ACCENT))
    
    section.title = titleText
    section.line = line
    section.contentY = -45 -- 内容起始Y位置
    
    return section
end

-- 工具函数：创建复选框
function SettingsUI:CreateCheckbox(parent, text, configPath, tooltip, x, y)
    local checkbox = CreateFrame("CheckButton", nil, parent, "InterfaceOptionsCheckButtonTemplate")
    checkbox:SetPoint("TOPLEFT", x or 15, y or -50)
    
    -- 文字
    local label = checkbox:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    label:SetPoint("LEFT", checkbox, "RIGHT", 5, 0)
    label:SetText(text)
    label:SetTextColor(unpack(COLORS.TEXT))
    
    -- 设置当前值
    checkbox:SetChecked(addon.Config:Get(configPath, false))
    
    -- 点击事件
    checkbox:SetScript("OnClick", function(self)
        addon.Config:Set(configPath, self:GetChecked())
        addon:FireEvent("CONFIG_CHANGED", configPath, self:GetChecked())
    end)
    
    -- 工具提示
    if tooltip then
        checkbox:SetScript("OnEnter", function(self)
            GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
            GameTooltip:SetText(tooltip)
            GameTooltip:Show()
        end)
        
        checkbox:SetScript("OnLeave", function()
            GameTooltip:Hide()
        end)
    end
    
    return checkbox
end

-- 工具函数：创建滑动条
function SettingsUI:CreateSlider(parent, text, configPath, min, max, step, x, y)
    local slider = CreateFrame("Slider", nil, parent, "OptionsSliderTemplate")
    slider:SetPoint("TOPLEFT", x or 15, y or -50)
    slider:SetSize(200, 20)
    slider:SetMinMaxValues(min, max)
    slider:SetValueStep(step or 1)
    slider:SetValue(addon.Config:Get(configPath, min))
    
    -- 标签
    local label = slider:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    label:SetPoint("BOTTOMLEFT", slider, "TOPLEFT", 0, 5)
    label:SetText(text)
    label:SetTextColor(unpack(COLORS.TEXT))
    
    -- 值显示
    local valueText = slider:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
    valueText:SetPoint("BOTTOMRIGHT", slider, "TOPRIGHT", 0, 5)
    valueText:SetText(slider:GetValue())
    valueText:SetTextColor(unpack(COLORS.ACCENT))
    
    -- 事件
    slider:SetScript("OnValueChanged", function(self, value)
        valueText:SetText(math.floor(value * 100) / 100)
        addon.Config:Set(configPath, value)
        addon:FireEvent("CONFIG_CHANGED", configPath, value)
    end)
    
    return slider
end

-- 创建频道管理标签页
function addon:CreateChannelTab(parent)
    local yOffset = -10

    -- 基本设置节
    local basicSection = SettingsUI:CreateSection(parent, "基本设置", yOffset)
    basicSection:SetHeight(120)

    SettingsUI:CreateCheckbox(basicSection, "启用自动加入频道", "channels.autoJoinEnabled",
        "登录时自动加入预设频道", 15, -50)

    SettingsUI:CreateCheckbox(basicSection, "启用自动排序", "channels.autoSortEnabled",
        "自动按照设定顺序排列频道", 15, -75)

    SettingsUI:CreateSlider(basicSection, "加入延迟 (秒)", "channels.joinDelay", 1, 30, 1, 300, -50)

    yOffset = yOffset - 130

    -- 频道顺序节
    local orderSection = SettingsUI:CreateSection(parent, "频道顺序", yOffset)
    orderSection:SetHeight(200)

    -- 创建可拖拽的频道列表
    addon:CreateChannelOrderList(orderSection)

    yOffset = yOffset - 210

    -- 频道重命名节
    local nameSection = SettingsUI:CreateSection(parent, "频道重命名", yOffset)
    nameSection:SetHeight(150)

    addon:CreateChannelNameSettings(nameSection)
end

-- 创建聊天功能标签页
function addon:CreateChatTab(parent)
    local yOffset = -10

    -- 时间戳设置
    local timestampSection = SettingsUI:CreateSection(parent, "时间戳设置", yOffset)
    timestampSection:SetHeight(150)

    SettingsUI:CreateCheckbox(timestampSection, "启用时间戳", "chat.timestamp.enabled",
        "在聊天消息前显示时间戳", 15, -50)

    -- 时间戳格式下拉菜单
    addon:CreateTimestampFormatDropdown(timestampSection, 15, -80)

    yOffset = yOffset - 160

    -- 过滤器设置
    local filterSection = SettingsUI:CreateSection(parent, "消息过滤", yOffset)
    filterSection:SetHeight(200)

    SettingsUI:CreateCheckbox(filterSection, "启用消息过滤", "chat.filters.enabled",
        "过滤包含屏蔽词的消息", 15, -50)

    addon:CreateFilterWordsList(filterSection)

    yOffset = yOffset - 210

    -- 关键词高亮
    local highlightSection = SettingsUI:CreateSection(parent, "关键词高亮", yOffset)
    highlightSection:SetHeight(150)

    addon:CreateHighlightKeywordsList(highlightSection)
end

-- 创建界面设置标签页
function addon:CreateUITab(parent)
    local yOffset = -10

    -- 小地图按钮设置
    local minimapSection = SettingsUI:CreateSection(parent, "小地图按钮", yOffset)
    minimapSection:SetHeight(100)

    SettingsUI:CreateCheckbox(minimapSection, "显示小地图按钮", "ui.minimapButton.hide",
        "在小地图周围显示快速访问按钮", 15, -50)

    SettingsUI:CreateSlider(minimapSection, "按钮位置", "ui.minimapButton.position", 0, 360, 5, 15, -80)

    yOffset = yOffset - 110

    -- 战斗文字设置
    local combatSection = SettingsUI:CreateSection(parent, "战斗文字", yOffset)
    combatSection:SetHeight(100)

    SettingsUI:CreateCheckbox(combatSection, "隐藏玩家头像伤害数字", "ui.combatText.hidePlayerFrame",
        "隐藏玩家头像上的伤害数字", 15, -50)

    SettingsUI:CreateCheckbox(combatSection, "隐藏宠物头像伤害数字", "ui.combatText.hidePetFrame",
        "隐藏宠物头像上的伤害数字", 15, -75)

    yOffset = yOffset - 110

    -- 动作条按钮设置
    local actionSection = SettingsUI:CreateSection(parent, "动作条按钮", yOffset)
    actionSection:SetHeight(120)

    SettingsUI:CreateCheckbox(actionSection, "启用按钮字体优化", "ui.actionButtons.enabled",
        "优化动作条按钮的快捷键和层数显示", 15, -50)

    SettingsUI:CreateSlider(actionSection, "字体大小", "ui.actionButtons.fontSize", 8, 20, 1, 15, -80)
end

-- 创建高级设置标签页
function addon:CreateAdvancedTab(parent)
    local yOffset = -10

    -- 调试设置
    local debugSection = SettingsUI:CreateSection(parent, "调试设置", yOffset)
    debugSection:SetHeight(80)

    SettingsUI:CreateCheckbox(debugSection, "启用调试模式", "advanced.debugMode",
        "显示详细的调试信息", 15, -50)

    yOffset = yOffset - 90

    -- 备份设置
    local backupSection = SettingsUI:CreateSection(parent, "备份设置", yOffset)
    backupSection:SetHeight(100)

    SettingsUI:CreateCheckbox(backupSection, "自动备份配置", "advanced.autoBackup",
        "定期自动备份配置文件", 15, -50)

    SettingsUI:CreateSlider(backupSection, "备份间隔 (天)", "advanced.backupInterval", 1, 30, 1, 15, -80)

    yOffset = yOffset - 110

    -- 导入导出
    local importExportSection = SettingsUI:CreateSection(parent, "导入导出", yOffset)
    importExportSection:SetHeight(180)

    addon:CreateImportExportButtons(importExportSection)

    yOffset = yOffset - 190

    -- 备份管理
    local backupSection = SettingsUI:CreateSection(parent, "备份管理", yOffset)
    backupSection:SetHeight(120)

    addon:CreateBackupButtons(backupSection)
end

-- 创建频道顺序列表
function addon:CreateChannelOrderList(parent)
    local channels = {
        {name = "综合", color = {1, 1, 1}},
        {name = "交易", color = {1, 0.7, 0}},
        {name = "本地防务", color = {0.3, 1, 0.3}},
        {name = "大脚世界频道", color = {1, 1, 0}}
    }

    local items = {}
    local itemHeight = 30

    for i, channel in ipairs(channels) do
        local item = CreateFrame("Frame", nil, parent)
        item:SetSize(400, itemHeight)
        item:SetPoint("TOPLEFT", 15, -50 - (i-1) * (itemHeight + 5))

        -- 背景
        local bg = item:CreateTexture(nil, "BACKGROUND")
        bg:SetAllPoints()
        bg:SetColorTexture(0.2, 0.2, 0.2, 0.5)

        -- 拖拽手柄
        local handle = item:CreateTexture(nil, "ARTWORK")
        handle:SetSize(20, 20)
        handle:SetPoint("LEFT", 5, 0)
        handle:SetTexture("Interface\\Buttons\\UI-GuildButton-PublicNote-Up")

        -- 频道名称
        local text = item:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        text:SetPoint("LEFT", handle, "RIGHT", 10, 0)
        text:SetText(channel.name)
        text:SetTextColor(unpack(channel.color))

        -- 位置输入框
        local posInput = CreateFrame("EditBox", nil, item, "InputBoxTemplate")
        posInput:SetSize(40, 20)
        posInput:SetPoint("RIGHT", -10, 0)
        posInput:SetAutoFocus(false)
        posInput:SetNumeric(true)
        posInput:SetText(addon.Config:Get("channels.channelOrder." .. channel.name, i))

        posInput:SetScript("OnEnterPressed", function(self)
            local value = tonumber(self:GetText()) or i
            addon.Config:Set("channels.channelOrder." .. channel.name, value)
            self:ClearFocus()
        end)

        -- 鼠标悬停效果
        item:EnableMouse(true)
        item:SetScript("OnEnter", function(self)
            bg:SetColorTexture(0.3, 0.3, 0.3, 0.7)
        end)
        item:SetScript("OnLeave", function(self)
            bg:SetColorTexture(0.2, 0.2, 0.2, 0.5)
        end)

        table.insert(items, item)
    end
end

-- 创建频道重命名设置
function addon:CreateChannelNameSettings(parent)
    local label = parent:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    label:SetPoint("TOPLEFT", 15, -50)
    label:SetText("大脚世界频道显示名称:")
    label:SetTextColor(unpack(COLORS.TEXT))

    local input = CreateFrame("EditBox", nil, parent, "InputBoxTemplate")
    input:SetSize(150, 20)
    input:SetPoint("LEFT", label, "RIGHT", 10, 0)
    input:SetAutoFocus(false)
    input:SetText(addon.Config:Get("channels.channelNames.大脚世界频道", "世界"))

    input:SetScript("OnEnterPressed", function(self)
        addon.Config:Set("channels.channelNames.大脚世界频道", self:GetText())
        self:ClearFocus()
    end)

    input:SetScript("OnEditFocusLost", function(self)
        addon.Config:Set("channels.channelNames.大脚世界频道", self:GetText())
    end)
end

-- 创建时间戳格式下拉菜单
function addon:CreateTimestampFormatDropdown(parent, x, y)
    local dropdown = CreateFrame("Frame", nil, parent, "UIDropDownMenuTemplate")
    dropdown:SetPoint("TOPLEFT", x, y)

    local label = parent:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    label:SetPoint("BOTTOMLEFT", dropdown, "TOPLEFT", 20, 5)
    label:SetText("时间戳格式:")
    label:SetTextColor(unpack(COLORS.TEXT))

    local formats = addon.Config:Get("chat.timestamp.formats", {})
    local currentFormat = addon.Config:Get("chat.timestamp.format", "[%H:%M:%S]")

    UIDropDownMenu_SetWidth(dropdown, 150)
    UIDropDownMenu_SetText(dropdown, currentFormat)

    UIDropDownMenu_Initialize(dropdown, function(self, level)
        for i, format in ipairs(formats) do
            local info = UIDropDownMenu_CreateInfo()
            info.text = format == "无" and "无" or (format .. " " .. date(format))
            info.value = format
            info.func = function()
                addon.Config:Set("chat.timestamp.format", format)
                UIDropDownMenu_SetText(dropdown, format)
            end
            info.checked = (format == currentFormat)
            UIDropDownMenu_AddButton(info, level)
        end
    end)
end

-- 创建过滤词列表
function addon:CreateFilterWordsList(parent)
    local label = parent:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    label:SetPoint("TOPLEFT", 15, -80)
    label:SetText("屏蔽词列表 (每行一个):")
    label:SetTextColor(unpack(COLORS.TEXT))

    local scrollFrame = CreateFrame("ScrollFrame", nil, parent, "UIPanelScrollFrameTemplate")
    scrollFrame:SetPoint("TOPLEFT", label, "BOTTOMLEFT", 0, -5)
    scrollFrame:SetSize(350, 80)

    local editBox = CreateFrame("EditBox", nil, scrollFrame)
    editBox:SetMultiLine(true)
    editBox:SetAutoFocus(false)
    editBox:SetFontObject(GameFontNormal)
    editBox:SetSize(330, 80)

    scrollFrame:SetScrollChild(editBox)

    -- 加载当前屏蔽词
    local blockedWords = addon.Config:Get("chat.filters.blockedWords", {})
    editBox:SetText(table.concat(blockedWords, "\n"))

    editBox:SetScript("OnEditFocusLost", function(self)
        local text = self:GetText()
        local words = {}
        for word in text:gmatch("[^\r\n]+") do
            if word:trim() ~= "" then
                table.insert(words, word:trim())
            end
        end
        addon.Config:Set("chat.filters.blockedWords", words)
    end)
end

-- 创建关键词高亮列表
function addon:CreateHighlightKeywordsList(parent)
    local label = parent:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    label:SetPoint("TOPLEFT", 15, -50)
    label:SetText("高亮关键词 (每行一个):")
    label:SetTextColor(unpack(COLORS.TEXT))

    local scrollFrame = CreateFrame("ScrollFrame", nil, parent, "UIPanelScrollFrameTemplate")
    scrollFrame:SetPoint("TOPLEFT", label, "BOTTOMLEFT", 0, -5)
    scrollFrame:SetSize(350, 60)

    local editBox = CreateFrame("EditBox", nil, scrollFrame)
    editBox:SetMultiLine(true)
    editBox:SetAutoFocus(false)
    editBox:SetFontObject(GameFontNormal)
    editBox:SetSize(330, 60)

    scrollFrame:SetScrollChild(editBox)

    -- 加载当前关键词
    local keywords = addon.Config:Get("chat.filters.highlightKeywords", {})
    editBox:SetText(table.concat(keywords, "\n"))

    editBox:SetScript("OnEditFocusLost", function(self)
        local text = self:GetText()
        local words = {}
        for word in text:gmatch("[^\r\n]+") do
            if word:trim() ~= "" then
                table.insert(words, word:trim())
            end
        end
        addon.Config:Set("chat.filters.highlightKeywords", words)
    end)
end

-- 创建导入导出按钮
function addon:CreateImportExportButtons(parent)
    -- 导出按钮
    local exportBtn = CreateFrame("Button", nil, parent, "UIPanelButtonTemplate")
    exportBtn:SetSize(100, 25)
    exportBtn:SetPoint("TOPLEFT", 15, -50)
    exportBtn:SetText("导出配置")

    exportBtn:SetScript("OnClick", function()
        local exportData = addon.Config:Export()
        addon:ShowExportDialog(exportData)
    end)

    -- 导入按钮
    local importBtn = CreateFrame("Button", nil, parent, "UIPanelButtonTemplate")
    importBtn:SetSize(100, 25)
    importBtn:SetPoint("LEFT", exportBtn, "RIGHT", 10, 0)
    importBtn:SetText("导入配置")

    importBtn:SetScript("OnClick", function()
        addon:ShowImportDialog()
    end)

    -- 重置按钮
    local resetBtn = CreateFrame("Button", nil, parent, "UIPanelButtonTemplate")
    resetBtn:SetSize(100, 25)
    resetBtn:SetPoint("LEFT", importBtn, "RIGHT", 10, 0)
    resetBtn:SetText("重置配置")

    resetBtn:SetScript("OnClick", function()
        StaticPopup_Show("CHATMANAGER_RESET_CONFIG")
    end)
end

-- 创建备份管理按钮
function addon:CreateBackupButtons(parent)
    -- 创建备份按钮
    local createBackupBtn = CreateFrame("Button", nil, parent, "UIPanelButtonTemplate")
    createBackupBtn:SetSize(120, 25)
    createBackupBtn:SetPoint("TOPLEFT", 15, -50)
    createBackupBtn:SetText("创建备份")

    createBackupBtn:SetScript("OnClick", function()
        local backup = addon.BackupManager:CreateBackup()
        print("|cff00ff00[ChatManager]|r 备份已创建: " .. backup.name)
    end)

    -- 备份管理按钮
    local manageBackupBtn = CreateFrame("Button", nil, parent, "UIPanelButtonTemplate")
    manageBackupBtn:SetSize(120, 25)
    manageBackupBtn:SetPoint("LEFT", createBackupBtn, "RIGHT", 10, 0)
    manageBackupBtn:SetText("管理备份")

    manageBackupBtn:SetScript("OnClick", function()
        addon.BackupManager:ShowBackupWindow()
    end)

    -- 导入备份按钮
    local importBackupBtn = CreateFrame("Button", nil, parent, "UIPanelButtonTemplate")
    importBackupBtn:SetSize(120, 25)
    importBackupBtn:SetPoint("TOPLEFT", createBackupBtn, "BOTTOMLEFT", 0, -10)
    importBackupBtn:SetText("导入备份")

    importBackupBtn:SetScript("OnClick", function()
        addon:ShowImportBackupDialog()
    end)

    -- 备份统计信息
    local stats = addon.BackupManager:GetBackupStats()
    local statsText = parent:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
    statsText:SetPoint("TOPLEFT", importBackupBtn, "BOTTOMLEFT", 0, -10)
    statsText:SetText(string.format("当前备份: %d 个 | 总大小: %.1f KB",
        stats.totalBackups, stats.totalSize / 1024))
    statsText:SetTextColor(0.7, 0.7, 0.7, 1)
end

-- 显示导出对话框
function addon:ShowExportDialog(data)
    local frame = CreateFrame("Frame", "ChatManagerExportDialog", UIParent, "BasicFrameTemplateWithInset")
    frame:SetSize(500, 400)
    frame:SetPoint("CENTER")
    frame:SetMovable(true)
    frame:EnableMouse(true)
    frame:RegisterForDrag("LeftButton")
    frame:SetScript("OnDragStart", frame.StartMoving)
    frame:SetScript("OnDragStop", frame.StopMovingOrSizing)
    frame:SetFrameStrata("DIALOG")

    frame.title = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    frame.title:SetPoint("TOPLEFT", frame.TitleBg, "TOPLEFT", 5, -5)
    frame.title:SetText("导出配置")

    local label = frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    label:SetPoint("TOPLEFT", frame.Inset, "TOPLEFT", 10, -10)
    label:SetText("复制以下配置数据:")

    local scrollFrame = CreateFrame("ScrollFrame", nil, frame.Inset, "UIPanelScrollFrameTemplate")
    scrollFrame:SetPoint("TOPLEFT", label, "BOTTOMLEFT", 0, -5)
    scrollFrame:SetPoint("BOTTOMRIGHT", frame.Inset, "BOTTOMRIGHT", -25, 40)

    local editBox = CreateFrame("EditBox", nil, scrollFrame)
    editBox:SetMultiLine(true)
    editBox:SetAutoFocus(true)
    editBox:SetFontObject(GameFontNormalSmall)
    editBox:SetText(data)
    editBox:HighlightText()

    scrollFrame:SetScrollChild(editBox)
    editBox:SetSize(scrollFrame:GetSize())

    local closeBtn = CreateFrame("Button", nil, frame, "UIPanelButtonTemplate")
    closeBtn:SetSize(80, 25)
    closeBtn:SetPoint("BOTTOMRIGHT", frame.Inset, "BOTTOMRIGHT", -10, 10)
    closeBtn:SetText("关闭")
    closeBtn:SetScript("OnClick", function() frame:Hide() end)
end

-- 显示导入对话框
function addon:ShowImportDialog()
    local frame = CreateFrame("Frame", "ChatManagerImportDialog", UIParent, "BasicFrameTemplateWithInset")
    frame:SetSize(500, 400)
    frame:SetPoint("CENTER")
    frame:SetMovable(true)
    frame:EnableMouse(true)
    frame:RegisterForDrag("LeftButton")
    frame:SetScript("OnDragStart", frame.StartMoving)
    frame:SetScript("OnDragStop", frame.StopMovingOrSizing)
    frame:SetFrameStrata("DIALOG")

    frame.title = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    frame.title:SetPoint("TOPLEFT", frame.TitleBg, "TOPLEFT", 5, -5)
    frame.title:SetText("导入配置")

    local label = frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    label:SetPoint("TOPLEFT", frame.Inset, "TOPLEFT", 10, -10)
    label:SetText("粘贴配置数据:")

    local scrollFrame = CreateFrame("ScrollFrame", nil, frame.Inset, "UIPanelScrollFrameTemplate")
    scrollFrame:SetPoint("TOPLEFT", label, "BOTTOMLEFT", 0, -5)
    scrollFrame:SetPoint("BOTTOMRIGHT", frame.Inset, "BOTTOMRIGHT", -25, 40)

    local editBox = CreateFrame("EditBox", nil, scrollFrame)
    editBox:SetMultiLine(true)
    editBox:SetAutoFocus(true)
    editBox:SetFontObject(GameFontNormalSmall)

    scrollFrame:SetScrollChild(editBox)
    editBox:SetSize(scrollFrame:GetSize())

    local importBtn = CreateFrame("Button", nil, frame, "UIPanelButtonTemplate")
    importBtn:SetSize(80, 25)
    importBtn:SetPoint("BOTTOMRIGHT", frame.Inset, "BOTTOMRIGHT", -100, 10)
    importBtn:SetText("导入")

    importBtn:SetScript("OnClick", function()
        local data = editBox:GetText()
        local success, message = addon.Config:Import(data)

        if success then
            print("|cff00ff00[ChatManager]|r " .. message)
            frame:Hide()
            -- 重新加载设置界面
            if addon.settingsPanel then
                addon.settingsPanel:Hide()
                addon.settingsPanel = nil
                addon:ShowSettingsPanel()
            end
        else
            print("|cffff0000[ChatManager]|r " .. message)
        end
    end)

    local closeBtn = CreateFrame("Button", nil, frame, "UIPanelButtonTemplate")
    closeBtn:SetSize(80, 25)
    closeBtn:SetPoint("RIGHT", importBtn, "LEFT", -10, 0)
    closeBtn:SetText("取消")
    closeBtn:SetScript("OnClick", function() frame:Hide() end)
end

-- 静态弹窗定义
StaticPopupDialogs["CHATMANAGER_RESET_CONFIG"] = {
    text = "确定要重置所有配置吗？这将清除所有自定义设置。",
    button1 = "确定",
    button2 = "取消",
    OnAccept = function()
        addon.Config:Reset()
        print("|cff00ff00[ChatManager]|r 配置已重置")
        -- 重新加载设置界面
        if addon.settingsPanel then
            addon.settingsPanel:Hide()
            addon.settingsPanel = nil
            addon:ShowSettingsPanel()
        end
    end,
    timeout = 0,
    whileDead = true,
    hideOnEscape = true,
    preferredIndex = 3,
}

-- 显示导入备份对话框
function addon:ShowImportBackupDialog()
    local frame = CreateFrame("Frame", "ChatManagerImportBackupDialog", UIParent, "BasicFrameTemplateWithInset")
    frame:SetSize(500, 400)
    frame:SetPoint("CENTER")
    frame:SetMovable(true)
    frame:EnableMouse(true)
    frame:RegisterForDrag("LeftButton")
    frame:SetScript("OnDragStart", frame.StartMoving)
    frame:SetScript("OnDragStop", frame.StopMovingOrSizing)
    frame:SetFrameStrata("DIALOG")

    frame.title = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    frame.title:SetPoint("TOPLEFT", frame.TitleBg, "TOPLEFT", 5, -5)
    frame.title:SetText("导入备份")

    local label = frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    label:SetPoint("TOPLEFT", frame.Inset, "TOPLEFT", 10, -10)
    label:SetText("粘贴备份数据:")

    local scrollFrame = CreateFrame("ScrollFrame", nil, frame.Inset, "UIPanelScrollFrameTemplate")
    scrollFrame:SetPoint("TOPLEFT", label, "BOTTOMLEFT", 0, -5)
    scrollFrame:SetPoint("BOTTOMRIGHT", frame.Inset, "BOTTOMRIGHT", -25, 40)

    local editBox = CreateFrame("EditBox", nil, scrollFrame)
    editBox:SetMultiLine(true)
    editBox:SetAutoFocus(true)
    editBox:SetFontObject(GameFontNormalSmall)

    scrollFrame:SetScrollChild(editBox)
    editBox:SetSize(scrollFrame:GetSize())

    local importBtn = CreateFrame("Button", nil, frame, "UIPanelButtonTemplate")
    importBtn:SetSize(80, 25)
    importBtn:SetPoint("BOTTOMRIGHT", frame.Inset, "BOTTOMRIGHT", -100, 10)
    importBtn:SetText("导入")

    importBtn:SetScript("OnClick", function()
        local data = editBox:GetText()
        local success, message = addon.BackupManager:ImportBackup(data)

        if success then
            print("|cff00ff00[ChatManager]|r " .. message)
            frame:Hide()
            -- 刷新备份管理窗口
            if addon.BackupManager.backupFrame and addon.BackupManager.backupFrame:IsShown() then
                addon.BackupManager:RefreshBackupList()
            end
        else
            print("|cffff0000[ChatManager]|r " .. message)
        end
    end)

    local closeBtn = CreateFrame("Button", nil, frame, "UIPanelButtonTemplate")
    closeBtn:SetSize(80, 25)
    closeBtn:SetPoint("RIGHT", importBtn, "LEFT", -10, 0)
    closeBtn:SetText("取消")
    closeBtn:SetScript("OnClick", function() frame:Hide() end)
end

-- 字符串工具函数
if not string.trim then
    function string:trim()
        return self:match("^%s*(.-)%s*$")
    end
end
