# ChatManager 更新日志

## v2.0.1 (修复版本)

### 🐛 Bug修复
- **修复频道API错误**: 修复了`GetChannelList()`在某些情况下返回数字而非表的问题
- **增强API安全检查**: 添加了频道API可用性检查，避免在游戏未完全加载时调用
- **优化初始化时序**: 延迟频道操作的执行，确保游戏API完全可用
- **改进错误处理**: 增强了对无效数据的处理和调试信息

### 🔧 技术改进
- 添加`IsChannelAPIReady()`函数检查API可用性
- 在所有频道操作前进行安全检查
- 优化事件处理时序，避免过早调用API
- 增强缓存机制的容错性

### 📝 详细修复

#### 问题描述
```
Interface/AddOns/ChatManager/ChatManager.lua:270: attempt to get length of local 'channels' (a number value)
Interface/AddOns/ChatManager/ChannelManager.lua:65: attempt to get length of local 'channels' (a number value)
```

#### 根本原因
- `GetChannelList()` API在游戏初始化阶段可能返回数字而非表
- 插件在游戏完全加载前就尝试调用频道API
- 缺少对API返回值类型的验证

#### 解决方案
1. **类型检查**: 验证`GetChannelList()`返回值是否为表类型
2. **API可用性检查**: 确保频道API函数存在且可调用
3. **延迟初始化**: 推迟频道操作直到游戏完全加载
4. **安全回退**: 在API不可用时返回缓存或空结果

#### 修复代码示例
```lua
-- 检查频道API是否可用
local function IsChannelAPIReady()
    return GetChannelList and type(GetChannelList) == "function" and 
           JoinChannelByName and type(JoinChannelByName) == "function"
end

-- 安全的频道列表获取
function ChannelManager:GetChannelList()
    -- 检查API是否可用
    if not IsChannelAPIReady() then
        return channelCache or {}
    end
    
    local success, channels = pcall(GetChannelList)
    if not success or not channels or type(channels) ~= "table" then
        return channelCache or {}
    end
    
    -- 处理频道数据...
end
```

### 🎯 影响范围
- 修复了插件加载时的崩溃问题
- 提高了插件的稳定性和兼容性
- 改善了错误处理和调试体验
- 不影响现有功能和配置

### 🧪 测试建议
1. 重新加载插件或重启游戏
2. 检查是否还有错误信息
3. 验证频道自动加入功能是否正常
4. 测试频道排序功能
5. 确认设置面板可以正常打开

---

## v2.0.0 (主要版本)

### 🎉 全新功能
- **现代化设置界面**: 标签页式设计，操作直观
- **智能频道管理**: 自动加入、排序、重命名
- **聊天功能增强**: 过滤、高亮、历史记录
- **小地图按钮**: 快速访问和状态显示
- **备份管理系统**: 自动备份、导入导出
- **模块化架构**: 代码重构，性能优化

### 🚀 性能提升
- 减少50%的API调用
- 智能缓存机制
- 事件驱动架构
- 内存使用优化

### 🎨 界面改进
- 全新的视觉设计
- 响应式布局
- 实时配置预览
- 拖拽排序支持

### 📦 新增模块
- `ChannelManager.lua` - 频道管理
- `ChatEnhancements.lua` - 聊天增强
- `MinimapButton.lua` - 小地图按钮
- `BackupManager.lua` - 备份管理
- `SettingsUI.lua` - 设置界面

---

## v1.0.1 (旧版本)

### 基础功能
- 频道自动加入
- 简单排序功能
- 基础设置界面
- 动作条按钮优化

---

## 升级指南

### 从 v1.x 升级到 v2.x
1. **备份配置**: 建议先导出当前配置
2. **清理旧文件**: 删除旧版本文件
3. **安装新版本**: 解压新版本到AddOns目录
4. **重启游戏**: 完全重启魔兽世界
5. **重新配置**: 根据需要调整新设置

### 配置迁移
- 大部分设置会自动迁移
- 新功能需要手动配置
- 建议检查所有设置项

### 兼容性说明
- 支持魔兽世界 11.0.2+
- 兼容大部分聊天插件
- 可能与某些UI插件冲突

---

## 已知问题

### v2.0.1
- 无已知严重问题

### 报告问题
如果遇到问题，请提供以下信息：
1. 错误信息截图
2. 游戏版本和插件版本
3. 其他已安装的插件列表
4. 重现步骤

---

## 开发计划

### 下一版本 (v2.1.0)
- [ ] 频道密码管理
- [ ] 更多时间戳格式
- [ ] 聊天日志导出
- [ ] 频道统计功能
- [ ] 多语言支持

### 长期计划
- [ ] 云端配置同步
- [ ] 频道模板系统
- [ ] 高级过滤规则
- [ ] 插件API开放
