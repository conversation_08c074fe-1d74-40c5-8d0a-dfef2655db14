local addonName, addon = ...

-- 备份管理模块
local BackupManager = {}
addon.BackupManager = BackupManager

-- 备份存储
local backupHistory = {}
local maxBackups = 10

-- 初始化备份管理器
function BackupManager:Initialize()
    self:LoadBackupHistory()
    self:SetupAutoBackup()
end

-- 加载备份历史
function BackupManager:LoadBackupHistory()
    -- 从SavedVariables加载备份历史
    if ChatManagerDB.backupHistory then
        backupHistory = ChatManagerDB.backupHistory
    else
        backupHistory = {}
        ChatManagerDB.backupHistory = backupHistory
    end
    
    -- 清理过期备份
    self:CleanupOldBackups()
end

-- 设置自动备份
function BackupManager:SetupAutoBackup()
    if not addon.Config:Get("advanced.autoBackup", true) then
        return
    end
    
    local interval = addon.Config:Get("advanced.backupInterval", 7) * 24 * 60 * 60 -- 转换为秒
    local lastBackup = self:GetLastBackupTime()
    local currentTime = time()
    
    if not lastBackup or (currentTime - lastBackup) >= interval then
        -- 延迟执行自动备份，避免登录时的性能影响
        C_Timer.After(30, function()
            self:CreateAutoBackup()
        end)
    end
end

-- 创建备份
function BackupManager:CreateBackup(name, description)
    local backup = {
        id = self:GenerateBackupId(),
        name = name or ("备份_" .. date("%Y%m%d_%H%M%S")),
        description = description or "手动备份",
        timestamp = time(),
        date = date("%Y-%m-%d %H:%M:%S"),
        version = addon.version,
        config = self:DeepCopy(ChatManagerDB),
        size = 0 -- 将在序列化后计算
    }
    
    -- 移除备份历史本身，避免递归
    backup.config.backupHistory = nil
    
    -- 计算备份大小
    local serialized = addon:Serialize(backup.config)
    backup.size = string.len(serialized)
    
    -- 添加到历史记录
    table.insert(backupHistory, backup)
    
    -- 限制备份数量
    while #backupHistory > maxBackups do
        table.remove(backupHistory, 1)
    end
    
    -- 保存到SavedVariables
    ChatManagerDB.backupHistory = backupHistory
    
    if addon.Config:Get("advanced.debugMode") then
        print("|cff00ff00[ChatManager]|r 备份已创建: " .. backup.name)
    end
    
    return backup
end

-- 创建自动备份
function BackupManager:CreateAutoBackup()
    local backup = self:CreateBackup(
        "自动备份_" .. date("%Y%m%d"),
        "系统自动创建的备份"
    )
    
    print("|cff00ff00[ChatManager]|r 自动备份已创建")
    return backup
end

-- 恢复备份
function BackupManager:RestoreBackup(backupId)
    local backup = self:GetBackup(backupId)
    if not backup then
        return false, "备份不存在"
    end
    
    -- 创建当前配置的备份
    local currentBackup = self:CreateBackup(
        "恢复前备份_" .. date("%Y%m%d_%H%M%S"),
        "恢复备份前的自动备份"
    )
    
    -- 恢复配置
    local restoredConfig = self:DeepCopy(backup.config)
    
    -- 保留备份历史
    restoredConfig.backupHistory = ChatManagerDB.backupHistory
    
    -- 应用恢复的配置
    ChatManagerDB = restoredConfig
    
    -- 触发配置重载事件
    addon:FireEvent("CONFIG_RESTORED", backup)
    
    return true, "备份恢复成功"
end

-- 删除备份
function BackupManager:DeleteBackup(backupId)
    for i, backup in ipairs(backupHistory) do
        if backup.id == backupId then
            table.remove(backupHistory, i)
            ChatManagerDB.backupHistory = backupHistory
            return true, "备份已删除"
        end
    end
    
    return false, "备份不存在"
end

-- 获取备份
function BackupManager:GetBackup(backupId)
    for _, backup in ipairs(backupHistory) do
        if backup.id == backupId then
            return backup
        end
    end
    return nil
end

-- 获取备份列表
function BackupManager:GetBackupList()
    local list = {}
    for _, backup in ipairs(backupHistory) do
        table.insert(list, {
            id = backup.id,
            name = backup.name,
            description = backup.description,
            date = backup.date,
            version = backup.version,
            size = backup.size
        })
    end
    
    -- 按时间倒序排列
    table.sort(list, function(a, b) return a.id > b.id end)
    
    return list
end

-- 导出备份
function BackupManager:ExportBackup(backupId)
    local backup = self:GetBackup(backupId)
    if not backup then
        return nil, "备份不存在"
    end
    
    local exportData = {
        type = "ChatManagerBackup",
        version = addon.version,
        backup = backup,
        exportTime = time(),
        exportDate = date("%Y-%m-%d %H:%M:%S")
    }
    
    return addon:Serialize(exportData), "备份导出成功"
end

-- 导入备份
function BackupManager:ImportBackup(data)
    local success, importData = addon:Deserialize(data)
    if not success then
        return false, "无效的备份数据"
    end
    
    if importData.type ~= "ChatManagerBackup" or not importData.backup then
        return false, "不是有效的备份文件"
    end
    
    local backup = importData.backup
    
    -- 生成新的ID避免冲突
    backup.id = self:GenerateBackupId()
    backup.name = backup.name .. "_导入"
    backup.description = (backup.description or "") .. " (从外部导入)"
    
    -- 添加到备份历史
    table.insert(backupHistory, backup)
    
    -- 限制备份数量
    while #backupHistory > maxBackups do
        table.remove(backupHistory, 1)
    end
    
    ChatManagerDB.backupHistory = backupHistory
    
    return true, "备份导入成功"
end

-- 清理过期备份
function BackupManager:CleanupOldBackups()
    local maxAge = addon.Config:Get("advanced.backupInterval", 7) * 3 * 24 * 60 * 60 -- 保留3倍间隔时间
    local currentTime = time()
    
    for i = #backupHistory, 1, -1 do
        local backup = backupHistory[i]
        if backup.timestamp and (currentTime - backup.timestamp) > maxAge then
            table.remove(backupHistory, i)
        end
    end
    
    ChatManagerDB.backupHistory = backupHistory
end

-- 获取最后备份时间
function BackupManager:GetLastBackupTime()
    local lastTime = 0
    for _, backup in ipairs(backupHistory) do
        if backup.timestamp and backup.timestamp > lastTime then
            lastTime = backup.timestamp
        end
    end
    return lastTime > 0 and lastTime or nil
end

-- 生成备份ID
function BackupManager:GenerateBackupId()
    return time() * 1000 + math.random(1, 999)
end

-- 深度复制
function BackupManager:DeepCopy(orig)
    local copy
    if type(orig) == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[self:DeepCopy(orig_key)] = self:DeepCopy(orig_value)
        end
        setmetatable(copy, self:DeepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- 获取备份统计信息
function BackupManager:GetBackupStats()
    local stats = {
        totalBackups = #backupHistory,
        totalSize = 0,
        oldestBackup = nil,
        newestBackup = nil,
        autoBackups = 0,
        manualBackups = 0
    }
    
    for _, backup in ipairs(backupHistory) do
        stats.totalSize = stats.totalSize + (backup.size or 0)
        
        if backup.description and backup.description:find("自动") then
            stats.autoBackups = stats.autoBackups + 1
        else
            stats.manualBackups = stats.manualBackups + 1
        end
        
        if not stats.oldestBackup or backup.timestamp < stats.oldestBackup.timestamp then
            stats.oldestBackup = backup
        end
        
        if not stats.newestBackup or backup.timestamp > stats.newestBackup.timestamp then
            stats.newestBackup = backup
        end
    end
    
    return stats
end

-- 显示备份管理窗口
function BackupManager:ShowBackupWindow()
    if self.backupFrame and self.backupFrame:IsShown() then
        self.backupFrame:Hide()
        return
    end
    
    self:CreateBackupWindow()
    self.backupFrame:Show()
end

-- 创建备份管理窗口
function BackupManager:CreateBackupWindow()
    if self.backupFrame then
        return
    end
    
    local frame = CreateFrame("Frame", "ChatManagerBackupFrame", UIParent, "BasicFrameTemplateWithInset")
    frame:SetSize(700, 500)
    frame:SetPoint("CENTER")
    frame:SetMovable(true)
    frame:EnableMouse(true)
    frame:RegisterForDrag("LeftButton")
    frame:SetScript("OnDragStart", frame.StartMoving)
    frame:SetScript("OnDragStop", frame.StopMovingOrSizing)
    frame:SetFrameStrata("DIALOG")
    
    frame.title = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    frame.title:SetPoint("TOPLEFT", frame.TitleBg, "TOPLEFT", 5, -5)
    frame.title:SetText("备份管理")
    
    -- 创建备份按钮
    local createBtn = CreateFrame("Button", nil, frame.Inset, "UIPanelButtonTemplate")
    createBtn:SetSize(100, 25)
    createBtn:SetPoint("TOPLEFT", frame.Inset, "TOPLEFT", 10, -10)
    createBtn:SetText("创建备份")
    
    createBtn:SetScript("OnClick", function()
        local backup = self:CreateBackup()
        self:RefreshBackupList()
        print("|cff00ff00[ChatManager]|r " .. backup.name .. " 已创建")
    end)
    
    -- 备份列表
    local scrollFrame = CreateFrame("ScrollFrame", nil, frame.Inset, "UIPanelScrollFrameTemplate")
    scrollFrame:SetPoint("TOPLEFT", createBtn, "BOTTOMLEFT", 0, -10)
    scrollFrame:SetPoint("BOTTOMRIGHT", frame.Inset, "BOTTOMRIGHT", -25, 10)
    
    local scrollChild = CreateFrame("Frame", nil, scrollFrame)
    scrollChild:SetSize(650, 1)
    scrollFrame:SetScrollChild(scrollChild)
    
    -- 刷新备份列表
    function self:RefreshBackupList()
        -- 清除现有内容
        for i, child in ipairs({scrollChild:GetChildren()}) do
            child:Hide()
            child:SetParent(nil)
        end
        
        local backups = self:GetBackupList()
        local yOffset = 0
        
        for i, backup in ipairs(backups) do
            local item = CreateFrame("Frame", nil, scrollChild)
            item:SetSize(620, 60)
            item:SetPoint("TOPLEFT", 5, yOffset)
            
            -- 背景
            local bg = item:CreateTexture(nil, "BACKGROUND")
            bg:SetAllPoints()
            bg:SetColorTexture(0.1, 0.1, 0.1, 0.3)
            
            -- 备份信息
            local nameText = item:CreateFontString(nil, "OVERLAY", "GameFontNormal")
            nameText:SetPoint("TOPLEFT", 10, -5)
            nameText:SetText(backup.name)
            nameText:SetTextColor(1, 1, 1, 1)
            
            local infoText = item:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
            infoText:SetPoint("TOPLEFT", nameText, "BOTTOMLEFT", 0, -2)
            infoText:SetText(backup.date .. " | " .. backup.description)
            infoText:SetTextColor(0.7, 0.7, 0.7, 1)
            
            local sizeText = item:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
            sizeText:SetPoint("TOPLEFT", infoText, "BOTTOMLEFT", 0, -2)
            sizeText:SetText("大小: " .. math.floor(backup.size / 1024) .. " KB | 版本: " .. backup.version)
            sizeText:SetTextColor(0.5, 0.5, 0.5, 1)
            
            -- 操作按钮
            local restoreBtn = CreateFrame("Button", nil, item, "UIPanelButtonTemplate")
            restoreBtn:SetSize(60, 20)
            restoreBtn:SetPoint("TOPRIGHT", -10, -5)
            restoreBtn:SetText("恢复")
            
            restoreBtn:SetScript("OnClick", function()
                StaticPopup_Show("CHATMANAGER_RESTORE_BACKUP", backup.name, nil, backup.id)
            end)
            
            local exportBtn = CreateFrame("Button", nil, item, "UIPanelButtonTemplate")
            exportBtn:SetSize(60, 20)
            exportBtn:SetPoint("RIGHT", restoreBtn, "LEFT", -5, 0)
            exportBtn:SetText("导出")
            
            exportBtn:SetScript("OnClick", function()
                local data, msg = self:ExportBackup(backup.id)
                if data then
                    addon:ShowExportDialog(data)
                else
                    print("|cffff0000[ChatManager]|r " .. msg)
                end
            end)
            
            local deleteBtn = CreateFrame("Button", nil, item, "UIPanelButtonTemplate")
            deleteBtn:SetSize(60, 20)
            deleteBtn:SetPoint("RIGHT", exportBtn, "LEFT", -5, 0)
            deleteBtn:SetText("删除")
            
            deleteBtn:SetScript("OnClick", function()
                StaticPopup_Show("CHATMANAGER_DELETE_BACKUP", backup.name, nil, backup.id)
            end)
            
            yOffset = yOffset - 65
        end
        
        scrollChild:SetHeight(math.abs(yOffset))
    end
    
    -- 初始加载
    self:RefreshBackupList()
    
    self.backupFrame = frame
end

-- 静态弹窗
StaticPopupDialogs["CHATMANAGER_RESTORE_BACKUP"] = {
    text = "确定要恢复备份 '%s' 吗？当前配置将被替换。",
    button1 = "确定",
    button2 = "取消",
    OnAccept = function(self, backupId)
        local success, msg = addon.BackupManager:RestoreBackup(backupId)
        print("|cff" .. (success and "00ff00" or "ff0000") .. "[ChatManager]|r " .. msg)
        if success then
            ReloadUI()
        end
    end,
    timeout = 0,
    whileDead = true,
    hideOnEscape = true,
    preferredIndex = 3,
}

StaticPopupDialogs["CHATMANAGER_DELETE_BACKUP"] = {
    text = "确定要删除备份 '%s' 吗？此操作无法撤销。",
    button1 = "确定",
    button2 = "取消",
    OnAccept = function(self, backupId)
        local success, msg = addon.BackupManager:DeleteBackup(backupId)
        print("|cff" .. (success and "00ff00" or "ff0000") .. "[ChatManager]|r " .. msg)
        if success and addon.BackupManager.backupFrame then
            addon.BackupManager:RefreshBackupList()
        end
    end,
    timeout = 0,
    whileDead = true,
    hideOnEscape = true,
    preferredIndex = 3,
}

-- 在插件准备就绪时初始化
addon:RegisterEvent("ADDON_READY", function()
    BackupManager:Initialize()
end)
