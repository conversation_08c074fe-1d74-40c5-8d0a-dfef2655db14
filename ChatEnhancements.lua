local addonName, addon = ...

-- 聊天增强模块
local ChatEnhancements = {}
addon.ChatEnhancements = ChatEnhancements

-- 消息历史存储
local messageHistory = {}
local maxHistoryLines = 1000

-- 初始化聊天增强功能
function ChatEnhancements:Initialize()
    self:SetupChatFilters()
    self:SetupMessageHistory()
    self:SetupKeywordHighlight()
    self:SetupTimestampDisplay()
    self:SetupChannelNameReplacement()
end

-- 设置聊天过滤器
function ChatEnhancements:SetupChatFilters()
    -- Hook所有聊天框的AddMessage方法
    for i = 1, NUM_CHAT_WINDOWS do
        local chatFrame = _G["ChatFrame" .. i]
        if chatFrame and chatFrame.AddMessage then
            local originalAddMessage = chatFrame.AddMessage
            
            chatFrame.AddMessage = function(frame, text, r, g, b, messageId, ...)
                if text then
                    -- 应用过滤器
                    if self:ShouldFilterMessage(text) then
                        return -- 不显示被过滤的消息
                    end
                    
                    -- 应用消息增强
                    text = self:EnhanceMessage(text, frame)
                    
                    -- 保存到历史记录
                    self:SaveToHistory(text, r, g, b, frame:GetName())
                end
                
                return originalAddMessage(frame, text, r, g, b, messageId, ...)
            end
        end
    end
end

-- 检查消息是否应该被过滤
function ChatEnhancements:ShouldFilterMessage(message)
    if not addon.Config:Get("chat.filters.enabled", false) then
        return false
    end
    
    local blockedWords = addon.Config:Get("chat.filters.blockedWords", {})
    local lowerMessage = message:lower()
    
    for _, word in ipairs(blockedWords) do
        if word ~= "" and lowerMessage:find(word:lower(), 1, true) then
            if addon.Config:Get("advanced.debugMode") then
                print("|cffff0000[ChatManager]|r 过滤消息: " .. word)
            end
            return true
        end
    end
    
    return false
end

-- 增强消息显示
function ChatEnhancements:EnhanceMessage(message, chatFrame)
    -- 应用时间戳
    message = self:ApplyTimestamp(message)
    
    -- 应用关键词高亮
    message = self:ApplyKeywordHighlight(message)
    
    -- 应用频道名称替换
    message = self:ApplyChannelNameReplacement(message)
    
    return message
end

-- 应用时间戳
function ChatEnhancements:ApplyTimestamp(message)
    if not addon.Config:Get("chat.timestamp.enabled", false) then
        return message
    end
    
    local format = addon.Config:Get("chat.timestamp.format", "[%H:%M:%S]")
    if format == "无" then
        return message
    end
    
    local color = addon.Config:Get("chat.timestamp.color", {1, 1, 1, 0.8})
    local timestamp = date(format)
    
    local colorCode = string.format("|cff%02x%02x%02x", 
        math.floor(color[1] * 255),
        math.floor(color[2] * 255),
        math.floor(color[3] * 255)
    )
    
    return colorCode .. timestamp .. "|r " .. message
end

-- 应用关键词高亮
function ChatEnhancements:ApplyKeywordHighlight(message)
    local keywords = addon.Config:Get("chat.filters.highlightKeywords", {})
    if #keywords == 0 then
        return message
    end
    
    for _, keyword in ipairs(keywords) do
        if keyword ~= "" then
            -- 使用不区分大小写的匹配
            local pattern = keyword:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1")
            message = message:gsub("(" .. pattern .. ")", "|cffff00ff%1|r")
        end
    end
    
    return message
end

-- 应用频道名称替换
function ChatEnhancements:ApplyChannelNameReplacement(message)
    local channelNames = addon.Config:Get("channels.channelNames", {})
    
    for originalName, customName in pairs(channelNames) do
        if customName and customName ~= "" then
            -- 替换频道标签中的名称
            local pattern = "|h%[(%d+)%. " .. originalName:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1") .. "%]|h"
            local replacement = "|h[%1. " .. customName .. "]|h"
            message = message:gsub(pattern, replacement)
        end
    end
    
    return message
end

-- 设置消息历史记录
function ChatEnhancements:SetupMessageHistory()
    maxHistoryLines = addon.Config:Get("chat.history.maxLines", 1000)
end

-- 保存消息到历史记录
function ChatEnhancements:SaveToHistory(message, r, g, b, frameName)
    if not addon.Config:Get("chat.history.enabled", true) then
        return
    end
    
    local historyEntry = {
        message = message,
        timestamp = time(),
        color = {r or 1, g or 1, b or 1},
        frame = frameName,
        date = date("%Y-%m-%d %H:%M:%S")
    }
    
    table.insert(messageHistory, historyEntry)
    
    -- 限制历史记录长度
    while #messageHistory > maxHistoryLines do
        table.remove(messageHistory, 1)
    end
end

-- 获取消息历史
function ChatEnhancements:GetMessageHistory(count)
    count = count or 50
    local history = {}
    local startIndex = math.max(1, #messageHistory - count + 1)
    
    for i = startIndex, #messageHistory do
        table.insert(history, messageHistory[i])
    end
    
    return history
end

-- 搜索消息历史
function ChatEnhancements:SearchHistory(keyword, maxResults)
    maxResults = maxResults or 20
    local results = {}
    local lowerKeyword = keyword:lower()
    
    for i = #messageHistory, 1, -1 do
        local entry = messageHistory[i]
        if entry.message:lower():find(lowerKeyword, 1, true) then
            table.insert(results, entry)
            if #results >= maxResults then
                break
            end
        end
    end
    
    return results
end

-- 设置关键词高亮
function ChatEnhancements:SetupKeywordHighlight()
    -- 监听配置变化
    addon:RegisterEvent("CONFIG_CHANGED", function(path, value)
        if path:find("chat.filters.highlightKeywords") then
            -- 关键词列表已更新
            if addon.Config:Get("advanced.debugMode") then
                print("|cff00ff00[ChatManager]|r 关键词高亮列表已更新")
            end
        end
    end)
end

-- 设置时间戳显示
function ChatEnhancements:SetupTimestampDisplay()
    -- 监听配置变化
    addon:RegisterEvent("CONFIG_CHANGED", function(path, value)
        if path:find("chat.timestamp") then
            if addon.Config:Get("advanced.debugMode") then
                print("|cff00ff00[ChatManager]|r 时间戳设置已更新")
            end
        end
    end)
end

-- 设置频道名称替换
function ChatEnhancements:SetupChannelNameReplacement()
    -- 监听配置变化
    addon:RegisterEvent("CONFIG_CHANGED", function(path, value)
        if path:find("channels.channelNames") then
            if addon.Config:Get("advanced.debugMode") then
                print("|cff00ff00[ChatManager]|r 频道名称设置已更新")
            end
        end
    end)
end

-- 显示消息历史窗口
function ChatEnhancements:ShowHistoryWindow()
    if self.historyFrame and self.historyFrame:IsShown() then
        self.historyFrame:Hide()
        return
    end
    
    self:CreateHistoryWindow()
    self.historyFrame:Show()
end

-- 创建消息历史窗口
function ChatEnhancements:CreateHistoryWindow()
    if self.historyFrame then
        return
    end
    
    local frame = CreateFrame("Frame", "ChatManagerHistoryFrame", UIParent, "BasicFrameTemplateWithInset")
    frame:SetSize(600, 400)
    frame:SetPoint("CENTER")
    frame:SetMovable(true)
    frame:EnableMouse(true)
    frame:RegisterForDrag("LeftButton")
    frame:SetScript("OnDragStart", frame.StartMoving)
    frame:SetScript("OnDragStop", frame.StopMovingOrSizing)
    frame:SetFrameStrata("DIALOG")
    
    frame.title = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    frame.title:SetPoint("TOPLEFT", frame.TitleBg, "TOPLEFT", 5, -5)
    frame.title:SetText("消息历史")
    
    -- 搜索框
    local searchBox = CreateFrame("EditBox", nil, frame.Inset, "InputBoxTemplate")
    searchBox:SetSize(200, 20)
    searchBox:SetPoint("TOPLEFT", frame.Inset, "TOPLEFT", 10, -10)
    searchBox:SetAutoFocus(false)
    searchBox:SetText("搜索...")
    
    -- 搜索按钮
    local searchBtn = CreateFrame("Button", nil, frame.Inset, "UIPanelButtonTemplate")
    searchBtn:SetSize(60, 22)
    searchBtn:SetPoint("LEFT", searchBox, "RIGHT", 5, 0)
    searchBtn:SetText("搜索")
    
    -- 历史记录列表
    local scrollFrame = CreateFrame("ScrollFrame", nil, frame.Inset, "UIPanelScrollFrameTemplate")
    scrollFrame:SetPoint("TOPLEFT", searchBox, "BOTTOMLEFT", 0, -10)
    scrollFrame:SetPoint("BOTTOMRIGHT", frame.Inset, "BOTTOMRIGHT", -25, 10)
    
    local scrollChild = CreateFrame("Frame", nil, scrollFrame)
    scrollChild:SetSize(550, 1)
    scrollFrame:SetScrollChild(scrollChild)
    
    -- 刷新历史记录显示
    local function RefreshHistory(searchKeyword)
        -- 清除现有内容
        for i, child in ipairs({scrollChild:GetChildren()}) do
            child:Hide()
            child:SetParent(nil)
        end
        
        local history
        if searchKeyword and searchKeyword ~= "" and searchKeyword ~= "搜索..." then
            history = self:SearchHistory(searchKeyword, 100)
        else
            history = self:GetMessageHistory(100)
        end
        
        local yOffset = 0
        for i, entry in ipairs(history) do
            local line = scrollChild:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
            line:SetPoint("TOPLEFT", 5, yOffset)
            line:SetPoint("TOPRIGHT", -5, yOffset)
            line:SetJustifyH("LEFT")
            line:SetText(entry.date .. " " .. entry.message)
            line:SetTextColor(unpack(entry.color))
            
            yOffset = yOffset - 15
        end
        
        scrollChild:SetHeight(math.abs(yOffset))
    end
    
    -- 搜索事件
    searchBtn:SetScript("OnClick", function()
        RefreshHistory(searchBox:GetText())
    end)
    
    searchBox:SetScript("OnEnterPressed", function(self)
        RefreshHistory(self:GetText())
        self:ClearFocus()
    end)
    
    -- 初始加载
    RefreshHistory()
    
    self.historyFrame = frame
end

-- 在插件加载时初始化
addon:RegisterEvent("ADDON_READY", function()
    ChatEnhancements:Initialize()
end)
