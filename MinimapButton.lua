local addonName, addon = ...

-- 小地图按钮模块
local MinimapButton = {}
addon.MinimapButton = MinimapButton

-- 创建小地图按钮
function addon:CreateMinimapButton()
    if self.minimapButton then
        return self.minimapButton
    end
    
    -- 创建主按钮
    local button = CreateFrame("Button", "ChatManagerMinimapButton", Minimap)
    button:SetSize(32, 32)
    button:SetFrameStrata("MEDIUM")
    button:SetFrameLevel(8)
    button:EnableMouse(true)
    button:RegisterForClicks("AnyUp")
    button:RegisterForDrag("LeftButton")
    
    -- 设置图标
    local icon = button:CreateTexture(nil, "BACKGROUND")
    icon:SetSize(20, 20)
    icon:SetPoint("CENTER", 0, 0)
    icon:SetTexture("Interface\\ChatFrame\\UI-ChatIcon-Chat")
    button.icon = icon
    
    -- 设置边框
    local border = button:CreateTexture(nil, "OVERLAY")
    border:SetSize(52, 52)
    border:SetPoint("TOPLEFT", 0, 0)
    border:SetTexture("Interface\\Minimap\\MiniMap-TrackingBorder")
    button.border = border
    
    -- 状态指示器
    local statusDot = button:CreateTexture(nil, "OVERLAY")
    statusDot:SetSize(8, 8)
    statusDot:SetPoint("TOPRIGHT", icon, "TOPRIGHT", 2, 2)
    statusDot:SetTexture("Interface\\Buttons\\UI-RadioButton")
    statusDot:SetTexCoord(0, 0.25, 0, 1)
    statusDot:SetVertexColor(0, 1, 0, 1) -- 绿色表示正常
    button.statusDot = statusDot
    
    -- 鼠标交互
    button:SetScript("OnClick", function(self, btn)
        if btn == "LeftButton" then
            addon:ToggleSettingsPanel()
        elseif btn == "RightButton" then
            addon:ShowQuickMenu(self)
        elseif btn == "MiddleButton" then
            addon.ChatEnhancements:ShowHistoryWindow()
        end
    end)
    
    -- 拖拽功能
    button:SetScript("OnDragStart", function(self)
        self:SetScript("OnUpdate", MinimapButton.OnUpdate)
    end)
    
    button:SetScript("OnDragStop", function(self)
        self:SetScript("OnUpdate", nil)
        -- 保存位置
        local angle = MinimapButton:GetButtonAngle(self)
        addon.Config:Set("ui.minimapButton.position", angle)
    end)
    
    -- 工具提示
    button:SetScript("OnEnter", function(self)
        GameTooltip:SetOwner(self, "ANCHOR_LEFT")
        GameTooltip:SetText("|cff00ff00聊天频道管理器|r")
        GameTooltip:AddLine("版本: " .. addon.version, 1, 1, 1)
        GameTooltip:AddLine(" ")
        GameTooltip:AddLine("|cffFFFFFF左键:|r 打开设置面板", 0.8, 0.8, 0.8)
        GameTooltip:AddLine("|cffFFFFFF右键:|r 快速菜单", 0.8, 0.8, 0.8)
        GameTooltip:AddLine("|cffFFFFFF中键:|r 消息历史", 0.8, 0.8, 0.8)
        GameTooltip:AddLine(" ")
        
        -- 显示状态信息
        local autoJoinEnabled = addon.Config:Get("channels.autoJoinEnabled", true)
        local autoSortEnabled = addon.Config:Get("channels.autoSortEnabled", true)
        local filtersEnabled = addon.Config:Get("chat.filters.enabled", false)
        
        GameTooltip:AddLine("状态:", 0.7, 0.7, 0.7)
        GameTooltip:AddLine("自动加入: " .. (autoJoinEnabled and "|cff00ff00开启|r" or "|cffff0000关闭|r"), 1, 1, 1)
        GameTooltip:AddLine("自动排序: " .. (autoSortEnabled and "|cff00ff00开启|r" or "|cffff0000关闭|r"), 1, 1, 1)
        GameTooltip:AddLine("消息过滤: " .. (filtersEnabled and "|cff00ff00开启|r" or "|cffff0000关闭|r"), 1, 1, 1)
        
        GameTooltip:Show()
    end)
    
    button:SetScript("OnLeave", function()
        GameTooltip:Hide()
    end)
    
    -- 设置初始位置
    local angle = addon.Config:Get("ui.minimapButton.position", 45)
    MinimapButton:SetButtonPosition(button, angle)
    
    -- 更新状态
    MinimapButton:UpdateStatus(button)
    
    self.minimapButton = button
    return button
end

-- 拖拽更新函数
function MinimapButton.OnUpdate(self)
    local mx, my = Minimap:GetCenter()
    local px, py = GetCursorPosition()
    local scale = Minimap:GetEffectiveScale()
    px, py = px / scale, py / scale
    
    local angle = math.atan2(py - my, px - mx)
    MinimapButton:SetButtonPosition(self, math.deg(angle))
end

-- 设置按钮位置
function MinimapButton:SetButtonPosition(button, angle)
    local radian = math.rad(angle)
    local x = math.cos(radian) * 80
    local y = math.sin(radian) * 80
    button:SetPoint("CENTER", Minimap, "CENTER", x, y)
end

-- 获取按钮角度
function MinimapButton:GetButtonAngle(button)
    local mx, my = Minimap:GetCenter()
    local bx, by = button:GetCenter()
    local angle = math.deg(math.atan2(by - my, bx - mx))
    return angle < 0 and angle + 360 or angle
end

-- 更新状态指示器
function MinimapButton:UpdateStatus(button)
    if not button or not button.statusDot then
        return
    end
    
    local autoJoinEnabled = addon.Config:Get("channels.autoJoinEnabled", true)
    local autoSortEnabled = addon.Config:Get("channels.autoSortEnabled", true)
    
    if autoJoinEnabled and autoSortEnabled then
        button.statusDot:SetVertexColor(0, 1, 0, 1) -- 绿色 - 全部启用
    elseif autoJoinEnabled or autoSortEnabled then
        button.statusDot:SetVertexColor(1, 1, 0, 1) -- 黄色 - 部分启用
    else
        button.statusDot:SetVertexColor(1, 0, 0, 1) -- 红色 - 全部禁用
    end
end

-- 显示快速菜单
function addon:ShowQuickMenu(anchor)
    if self.quickMenu and self.quickMenu:IsShown() then
        self.quickMenu:Hide()
        return
    end
    
    self:CreateQuickMenu()
    
    -- 定位菜单
    self.quickMenu:ClearAllPoints()
    self.quickMenu:SetPoint("TOPLEFT", anchor, "BOTTOMRIGHT", 5, -5)
    self.quickMenu:Show()
    
    -- 自动隐藏
    C_Timer.After(10, function()
        if self.quickMenu and self.quickMenu:IsShown() then
            self.quickMenu:Hide()
        end
    end)
end

-- 创建快速菜单
function addon:CreateQuickMenu()
    if self.quickMenu then
        return
    end
    
    local menu = CreateFrame("Frame", "ChatManagerQuickMenu", UIParent, "BasicFrameTemplate")
    menu:SetSize(200, 250)
    menu:SetFrameStrata("TOOLTIP")
    menu:SetFrameLevel(100)
    menu:EnableMouse(true)
    menu:Hide()
    
    -- 背景
    local bg = menu:CreateTexture(nil, "BACKGROUND")
    bg:SetAllPoints()
    bg:SetColorTexture(0.1, 0.1, 0.1, 0.95)
    
    -- 标题
    local title = menu:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    title:SetPoint("TOP", 0, -10)
    title:SetText("快速菜单")
    title:SetTextColor(0.3, 0.7, 1, 1)
    
    local yOffset = -35
    
    -- 快速切换选项
    local toggles = {
        {
            text = "自动加入频道",
            configPath = "channels.autoJoinEnabled",
            callback = function(enabled)
                if enabled then
                    addon.ChannelManager:AutoJoinChannels()
                end
            end
        },
        {
            text = "自动排序频道",
            configPath = "channels.autoSortEnabled",
            callback = function(enabled)
                if enabled then
                    addon.ChannelManager:SortChannels()
                end
            end
        },
        {
            text = "消息过滤",
            configPath = "chat.filters.enabled"
        },
        {
            text = "时间戳显示",
            configPath = "chat.timestamp.enabled"
        }
    }
    
    for _, toggle in ipairs(toggles) do
        local checkbox = CreateFrame("CheckButton", nil, menu, "InterfaceOptionsCheckButtonTemplate")
        checkbox:SetPoint("TOPLEFT", 15, yOffset)
        checkbox:SetChecked(addon.Config:Get(toggle.configPath, false))
        
        local label = checkbox:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        label:SetPoint("LEFT", checkbox, "RIGHT", 5, 0)
        label:SetText(toggle.text)
        label:SetTextColor(0.9, 0.9, 0.9, 1)
        
        checkbox:SetScript("OnClick", function(self)
            local enabled = self:GetChecked()
            addon.Config:Set(toggle.configPath, enabled)
            
            if toggle.callback then
                toggle.callback(enabled)
            end
            
            -- 更新小地图按钮状态
            addon.MinimapButton:UpdateStatus(addon.minimapButton)
        end)
        
        yOffset = yOffset - 30
    end
    
    -- 分隔线
    local separator = menu:CreateTexture(nil, "ARTWORK")
    separator:SetPoint("TOPLEFT", 10, yOffset - 5)
    separator:SetPoint("TOPRIGHT", -10, yOffset - 5)
    separator:SetHeight(1)
    separator:SetColorTexture(0.3, 0.3, 0.3, 1)
    
    yOffset = yOffset - 15
    
    -- 快速操作按钮
    local buttons = {
        {
            text = "立即加入频道",
            callback = function()
                addon.ChannelManager:AutoJoinChannels()
                menu:Hide()
            end
        },
        {
            text = "重新排序频道",
            callback = function()
                addon.ChannelManager:SortChannels()
                menu:Hide()
            end
        },
        {
            text = "消息历史",
            callback = function()
                addon.ChatEnhancements:ShowHistoryWindow()
                menu:Hide()
            end
        }
    }
    
    for _, btnInfo in ipairs(buttons) do
        local btn = CreateFrame("Button", nil, menu, "UIPanelButtonTemplate")
        btn:SetSize(170, 22)
        btn:SetPoint("TOPLEFT", 15, yOffset)
        btn:SetText(btnInfo.text)
        btn:SetScript("OnClick", btnInfo.callback)
        
        yOffset = yOffset - 27
    end
    
    -- 点击外部关闭
    menu:SetScript("OnShow", function(self)
        local closeFrame = CreateFrame("Frame", nil, UIParent)
        closeFrame:SetAllPoints(UIParent)
        closeFrame:EnableMouse(true)
        closeFrame:SetFrameStrata("TOOLTIP")
        closeFrame:SetFrameLevel(99)
        
        closeFrame:SetScript("OnMouseDown", function()
            menu:Hide()
            closeFrame:Hide()
        end)
        
        menu.closeFrame = closeFrame
    end)
    
    menu:SetScript("OnHide", function(self)
        if self.closeFrame then
            self.closeFrame:Hide()
        end
    end)
    
    self.quickMenu = menu
end

-- 监听配置变化以更新按钮状态
addon:RegisterEvent("CONFIG_CHANGED", function(path, value)
    if addon.minimapButton and (
        path:find("channels.autoJoinEnabled") or 
        path:find("channels.autoSortEnabled") or
        path:find("ui.minimapButton")
    ) then
        addon.MinimapButton:UpdateStatus(addon.minimapButton)
        
        -- 如果隐藏设置改变
        if path == "ui.minimapButton.hide" then
            if value then
                addon.minimapButton:Hide()
            else
                addon.minimapButton:Show()
            end
        end
        
        -- 如果位置改变
        if path == "ui.minimapButton.position" then
            addon.MinimapButton:SetButtonPosition(addon.minimapButton, value)
        end
    end
end)
