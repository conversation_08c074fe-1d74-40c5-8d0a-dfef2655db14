local addonName, addon = ...

-- 增强的频道管理模块
local ChannelManager = {}
addon.ChannelManager = ChannelManager

-- 频道状态缓存
local channelCache = {}
local lastUpdateTime = 0

-- 检查频道API是否可用
local function IsChannelAPIReady()
    return GetChannelList and type(GetChannelList) == "function" and
           JoinChannelByName and type(JoinChannelByName) == "function"
end

-- 初始化频道管理器
function ChannelManager:Initialize()
    self:SetupEventHandlers()
    self:LoadChannelCache()
end

-- 设置事件处理器
function ChannelManager:SetupEventHandlers()
    -- 监听频道相关事件
    addon.eventFrame:RegisterEvent("CHANNEL_UI_UPDATE")
    addon.eventFrame:RegisterEvent("CHAT_MSG_CHANNEL_NOTICE")
    addon.eventFrame:RegisterEvent("CHAT_MSG_CHANNEL_NOTICE_USER")
    
    -- 监听配置变化
    addon:RegisterEvent("CONFIG_CHANGED", function(path, value)
        if path:find("channels%.") then
            self:OnConfigChanged(path, value)
        end
    end)
end

-- 配置变化处理
function ChannelManager:OnConfigChanged(path, value)
    if path == "channels.autoJoinEnabled" and value then
        self:AutoJoinChannels()
    elseif path == "channels.autoSortEnabled" and value then
        C_Timer.After(1, function() self:SortChannels() end)
    elseif path:find("channels%.channelOrder%.") then
        if addon.Config:Get("channels.autoSortEnabled", true) then
            C_Timer.After(0.5, function() self:SortChannels() end)
        end
    elseif path:find("channels%.channelNames%.") then
        C_Timer.After(0.5, function() self:ApplyCustomNames() end)
    end
end

-- 获取当前频道列表（增强版）
function ChannelManager:GetChannelList()
    local currentTime = GetTime()

    -- 缓存1秒内的结果
    if currentTime - lastUpdateTime < 1 and next(channelCache) then
        return channelCache
    end

    -- 检查API是否可用
    if not IsChannelAPIReady() then
        if addon.Config:Get("advanced.debugMode") then
            print("|cffff0000[ChatManager]|r 频道API尚未准备就绪")
        end
        return channelCache
    end

    local success, channels = pcall(GetChannelList)
    if not success or not channels or type(channels) ~= "table" then
        if addon.Config:Get("advanced.debugMode") then
            print("|cffff0000[ChatManager]|r 获取频道列表失败或返回无效数据")
        end
        return channelCache -- 返回缓存的结果
    end

    channelCache = {}
    for i = 1, #channels, 3 do
        local id = channels[i]
        local name = channels[i + 1]
        local disabled = channels[i + 2]

        if id and name and not disabled then
            channelCache[name] = {
                id = id,
                name = name,
                position = math.ceil(i / 3),
                disabled = disabled,
                timestamp = currentTime
            }
        end
    end

    lastUpdateTime = currentTime
    return channelCache
end

-- 智能自动加入频道
function ChannelManager:AutoJoinChannels()
    if not addon.Config:Get("channels.autoJoinEnabled", true) then
        return
    end

    -- 检查API是否可用
    if not IsChannelAPIReady() then
        if addon.Config:Get("advanced.debugMode") then
            print("|cffff0000[ChatManager]|r 频道API尚未准备就绪，跳过自动加入")
        end
        return
    end

    local channels = addon.Config:Get("channels.autoJoinChannels", {})
    local delay = addon.Config:Get("channels.joinDelay", 5)
    local joinedCount = 0

    if addon.Config:Get("advanced.debugMode") then
        print("|cff00ff00[ChatManager]|r 开始自动加入频道，延迟 " .. delay .. " 秒")
    end

    C_Timer.After(delay, function()
        -- 再次检查API是否可用
        if not IsChannelAPIReady() then
            if addon.Config:Get("advanced.debugMode") then
                print("|cffff0000[ChatManager]|r 频道API不可用，取消自动加入")
            end
            return
        end

        for i, channelName in ipairs(channels) do
            -- 检查是否已经在频道中
            if not self:IsInChannel(channelName) then
                C_Timer.After(i * 0.5, function() -- 错开加入时间
                    local success = pcall(JoinChannelByName, channelName)
                    if success then
                        joinedCount = joinedCount + 1
                        if addon.Config:Get("advanced.debugMode") then
                            print("|cff00ff00[ChatManager]|r 已加入频道: " .. channelName)
                        end
                    else
                        if addon.Config:Get("advanced.debugMode") then
                            print("|cffff0000[ChatManager]|r 加入频道失败: " .. channelName)
                        end
                    end
                end)
            end
        end

        -- 加入完成后自动排序
        if addon.Config:Get("channels.autoSortEnabled", true) then
            C_Timer.After(#channels * 0.5 + 2, function()
                self:SortChannels()
            end)
        end
    end)
end

-- 检查是否在指定频道中
function ChannelManager:IsInChannel(channelName)
    local channelList = self:GetChannelList()
    return channelList[channelName] ~= nil
end

-- 智能频道排序
function ChannelManager:SortChannels()
    local channelMap = self:GetChannelList()
    local order = addon.Config:Get("channels.channelOrder", {})
    local sortOperations = {}
    
    -- 收集需要排序的频道
    for name, targetPos in pairs(order) do
        if channelMap[name] then
            local currentPos = channelMap[name].position
            if currentPos ~= targetPos then
                table.insert(sortOperations, {
                    id = channelMap[name].id,
                    name = name,
                    currentPos = currentPos,
                    targetPos = targetPos,
                    priority = math.abs(currentPos - targetPos) -- 优先处理位置差异大的
                })
            end
        end
    end
    
    if #sortOperations == 0 then
        if addon.Config:Get("advanced.debugMode") then
            print("|cff00ff00[ChatManager]|r 频道顺序已正确，无需排序")
        end
        return
    end
    
    -- 按优先级排序操作
    table.sort(sortOperations, function(a, b) 
        return a.priority > b.priority 
    end)
    
    -- 执行排序操作
    local successCount = 0
    for i, operation in ipairs(sortOperations) do
        C_Timer.After(i * 0.2, function() -- 错开操作时间
            local success = pcall(SwapChannelsByChannelIndex, operation.id, operation.targetPos)
            if success then
                successCount = successCount + 1
                if addon.Config:Get("advanced.debugMode") then
                    print("|cff00ff00[ChatManager]|r 频道排序: " .. operation.name .. 
                          " (" .. operation.currentPos .. " -> " .. operation.targetPos .. ")")
                end
            else
                if addon.Config:Get("advanced.debugMode") then
                    print("|cffff0000[ChatManager]|r 频道排序失败: " .. operation.name)
                end
            end
        end)
    end
    
    -- 清除缓存以便下次获取最新状态
    C_Timer.After(#sortOperations * 0.2 + 1, function()
        channelCache = {}
        lastUpdateTime = 0
    end)
end

-- 应用频道自定义名称
function ChannelManager:ApplyCustomNames()
    local channelMap = self:GetChannelList()
    local customNames = addon.Config:Get("channels.channelNames", {})
    local appliedCount = 0
    
    for originalName, customName in pairs(customNames) do
        if channelMap[originalName] and customName ~= "" then
            local success = pcall(SetChannelName, channelMap[originalName].id, customName)
            if success then
                appliedCount = appliedCount + 1
                if addon.Config:Get("advanced.debugMode") then
                    print("|cff00ff00[ChatManager]|r 频道重命名: " .. originalName .. " -> " .. customName)
                end
            end
        end
    end
    
    if appliedCount > 0 and addon.Config:Get("advanced.debugMode") then
        print("|cff00ff00[ChatManager]|r 已应用 " .. appliedCount .. " 个频道自定义名称")
    end
end

-- 更新频道（综合方法）
function ChannelManager:UpdateChannels()
    self:ApplyCustomNames()
    if addon.Config:Get("channels.autoSortEnabled", true) then
        self:SortChannels()
    end
end

-- 获取频道统计信息
function ChannelManager:GetChannelStats()
    local channelMap = self:GetChannelList()
    local autoJoinChannels = addon.Config:Get("channels.autoJoinChannels", {})
    local customNames = addon.Config:Get("channels.channelNames", {})
    
    local stats = {
        totalChannels = 0,
        joinedChannels = 0,
        autoJoinChannels = #autoJoinChannels,
        customNamedChannels = 0,
        channels = {}
    }
    
    -- 统计已加入的频道
    for name, info in pairs(channelMap) do
        stats.totalChannels = stats.totalChannels + 1
        stats.joinedChannels = stats.joinedChannels + 1
        
        local channelInfo = {
            name = name,
            id = info.id,
            position = info.position,
            isAutoJoin = tContains(autoJoinChannels, name),
            hasCustomName = customNames[name] ~= nil,
            customName = customNames[name]
        }
        
        if channelInfo.hasCustomName then
            stats.customNamedChannels = stats.customNamedChannels + 1
        end
        
        table.insert(stats.channels, channelInfo)
    end
    
    -- 按位置排序
    table.sort(stats.channels, function(a, b) return a.position < b.position end)
    
    return stats
end

-- 批量操作：加入多个频道
function ChannelManager:JoinChannels(channelList, callback)
    local joinCount = 0
    local totalChannels = #channelList
    
    for i, channelName in ipairs(channelList) do
        C_Timer.After(i * 0.5, function()
            if not self:IsInChannel(channelName) then
                local success = pcall(JoinChannelByName, channelName)
                if success then
                    joinCount = joinCount + 1
                end
            else
                joinCount = joinCount + 1 -- 已经在频道中
            end
            
            -- 检查是否完成
            if joinCount >= totalChannels and callback then
                callback(joinCount, totalChannels)
            end
        end)
    end
end

-- 批量操作：离开多个频道
function ChannelManager:LeaveChannels(channelList, callback)
    local leaveCount = 0
    local totalChannels = #channelList
    
    for i, channelName in ipairs(channelList) do
        C_Timer.After(i * 0.3, function()
            if self:IsInChannel(channelName) then
                local success = pcall(LeaveChannelByName, channelName)
                if success then
                    leaveCount = leaveCount + 1
                end
            else
                leaveCount = leaveCount + 1 -- 已经不在频道中
            end
            
            -- 检查是否完成
            if leaveCount >= totalChannels and callback then
                callback(leaveCount, totalChannels)
            end
        end)
    end
end

-- 重置频道顺序
function ChannelManager:ResetChannelOrder()
    local defaultOrder = {
        ["综合"] = 1,
        ["交易"] = 2,
        ["本地防务"] = 3,
        ["大脚世界频道"] = 4
    }
    
    addon.Config:Set("channels.channelOrder", defaultOrder)
    
    if addon.Config:Get("channels.autoSortEnabled", true) then
        C_Timer.After(0.5, function()
            self:SortChannels()
        end)
    end
end

-- 加载频道缓存
function ChannelManager:LoadChannelCache()
    -- 延迟初始化，确保游戏API可用
    C_Timer.After(2, function()
        self:GetChannelList()
    end)
end

-- 清除频道缓存
function ChannelManager:ClearCache()
    channelCache = {}
    lastUpdateTime = 0
end

-- 在插件准备就绪时初始化
addon:RegisterEvent("ADDON_READY", function()
    ChannelManager:Initialize()
end)

-- 在玩家登录时执行自动加入
addon:RegisterEvent("PLAYER_LOGIN", function()
    ChannelManager:AutoJoinChannels()
end)

-- 在进入世界时应用设置
addon:RegisterEvent("PLAYER_ENTERING_WORLD", function()
    C_Timer.After(2, function()
        ChannelManager:UpdateChannels()
    end)
end)
