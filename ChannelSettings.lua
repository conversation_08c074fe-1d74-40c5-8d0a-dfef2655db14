local addonName, addon = ...
local L = addon.L

local defaultSettings = {
    autoJoinChannels = true,
    swapChannels = true,
    replaceChannelName = true,
    showTimestamp = false,
    channelOrder = {
        ["综合"] = 1,
        ["交易"] = 2,
        ["本地防务"] = 3,
        ["大脚世界频道"] = 4
    },
    channelNames = { ["大脚世界频道"] = "世界" }
}

if not ChatManagerDB then
    ChatManagerDB = defaultSettings
else
    for key, value in pairs(defaultSettings) do
        if ChatManagerDB[key] == nil then
            ChatManagerDB[key] = value
        end
    end
end

--------------------------------------------------
-- 功能函数
--------------------------------------------------

-- 自动加入预定义频道（延迟5秒执行）
local function JoinChannels()
    if not ChatManagerDB.autoJoinChannels then
        return
    end
    C_Timer.After(5, function()
        JoinChannelByName("综合")
        JoinChannelByName("交易")
        JoinChannelByName("本地防务")
        JoinChannelByName("大脚世界频道")
    end)
end

-- 根据设置调整频道顺序
local function SwapChannels()
    if not ChatManagerDB.swapChannels then
        return
    end
    local orderedChannels = {}
    for name, order in pairs(ChatManagerDB.channelOrder) do
        orderedChannels[order] = name
    end
    for i = 1, 4 do
        local channelName = orderedChannels[i]
        if channelName then
            local id = GetChannelName(channelName)
            if id and id ~= i then
                C_ChatInfo.SwapChatChannelsByChannelIndex(id, i)
            end
        end
    end
end

-- 替换大脚世界频道名称为用户自定义简称（聊天消息中进行替换）
local function RenameBigfootChannel()
    if not ChatManagerDB.replaceChannelName then
        return
    end
    local newName = "世界"
    if ChatManagerDB.channelNames and ChatManagerDB.channelNames["大脚世界频道"] then
        newName = ChatManagerDB.channelNames["大脚世界频道"]
    end
    for i = 1, NUM_CHAT_WINDOWS do
        if i ~= 2 then -- 排除战斗记录窗口
            local chatFrame = _G["ChatFrame" .. i]
            local originalAddMessage = chatFrame.AddMessage
            chatFrame.AddMessage = function(frame, text, ...)
                if text then
                    text = text:gsub("|h%[(%d+)%. 大脚世界频道%]|h", "|h[%1%. " .. newName .. "]|h")
                end
                return originalAddMessage(frame, text, ...)
            end
        end
    end
end

--------------------------------------------------
-- 配置面板及拖拽排序UI
--------------------------------------------------

-- 创建拖拽调整频道顺序的列表
local function CreateDraggableChannelList(panel)
    local container = CreateFrame("Frame", "ChatManagerDraggableChannelList", panel, "BackdropTemplate")
    container:SetSize(360, 200)
    container:SetPoint("TOPLEFT", panel, "TOPLEFT", 16, -250)
    
    -- 使用游戏风格的背景
    container:SetBackdrop({
        bgFile = "Interface/Tooltips/UI-Tooltip-Background",
        edgeFile = "Interface/Tooltips/UI-Tooltip-Border",
        tile = true,
        tileSize = 16,
        edgeSize = 16,
        insets = {left = 4, right = 4, top = 4, bottom = 4}
    })
    container:SetBackdropColor(0, 0, 0, 0.8)
    container:SetBackdropBorderColor(0.5, 0.5, 0.5, 1)

    -- 添加标题
    local headerBg = container:CreateTexture(nil, "BACKGROUND")
    headerBg:SetPoint("TOPLEFT", 5, -5)
    headerBg:SetPoint("TOPRIGHT", -5, -5)
    headerBg:SetHeight(24)
    headerBg:SetColorTexture(0.2, 0.2, 0.2, 0.9)

    local header = container:CreateFontString(nil, "OVERLAY", "GameFontNormalMed2")
    header:SetPoint("TOPLEFT", headerBg, "TOPLEFT", 8, 0)
    header:SetPoint("BOTTOMRIGHT", headerBg, "BOTTOMRIGHT", -8, 0)
    header:SetJustifyH("LEFT")
    header:SetText("频道排序")
    header:SetTextColor(1, 0.82, 0)

    -- 定义频道列表
    local channels = {
        {name = "综合", color = {1, 1, 1}},        -- 白色
        {name = "交易", color = {1, 0.7, 0}},      -- 橙色
        {name = "本地防务", color = {0.3, 1, 0.3}},  -- 绿色
        {name = "大脚世界频道", color = {1, 1, 0}}   -- 黄色
    }

    -- 创建频道列表项
    local items = {}
    local itemHeight = 24
    local yOffset = -34  -- 标题下方的起始位置

    for i, channel in ipairs(channels) do
        -- 创建频道项容器
        local item = CreateFrame("Frame", nil, container)
        item:SetSize(340, itemHeight)
        item:SetPoint("TOPLEFT", container, "TOPLEFT", 10, yOffset - (i-1) * (itemHeight + 2))
        
        -- 复选框图标（用纹理模拟）
        local checkbox = item:CreateTexture(nil, "ARTWORK")
        checkbox:SetSize(16, 16)
        checkbox:SetPoint("LEFT", 4, 0)
        checkbox:SetTexture("Interface/Buttons/UI-CheckBox-Check")
        checkbox:SetVertexColor(1, 1, 1, 1)

        -- 频道名称
        local text = item:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        text:SetPoint("LEFT", checkbox, "RIGHT", 8, 0)
        text:SetText(channel.name)
        text:SetTextColor(unpack(channel.color))

        -- 鼠标悬停效果
        item:EnableMouse(true)
        item:SetScript("OnEnter", function(self)
            self:SetBackdrop({
                bgFile = "Interface/Buttons/UI-Listbox-Highlight",
                tile = true
            })
            self:SetBackdropColor(1, 1, 1, 0.3)
        end)
        item:SetScript("OnLeave", function(self)
            self:SetBackdrop(nil)
        end)

        -- 点击效果
        item:SetScript("OnMouseDown", function(self)
            text:SetPoint("LEFT", checkbox, "RIGHT", 9, -1)
        end)
        item:SetScript("OnMouseUp", function(self)
            text:SetPoint("LEFT", checkbox, "RIGHT", 8, 0)
        end)

        -- 存储频道信息
        item.channelName = channel.name
        item.order = i
        table.insert(items, item)
    end

    -- 更新排序函数
    function container:UpdateOrder()
        for i, item in ipairs(items) do
            ChatManagerDB.channelOrder[item.channelName] = i
        end
        SwapChannels()
    end

    -- 添加上下移动按钮
    for i, item in ipairs(items) do
        -- 上移按钮
        if i > 1 then
            local upButton = CreateFrame("Button", nil, item)
            upButton:SetSize(16, 16)
            upButton:SetPoint("RIGHT", item, "RIGHT", -20, 0)
            upButton:SetNormalTexture("Interface/Buttons/UI-ScrollBar-ScrollUpButton-Up")
            upButton:SetPushedTexture("Interface/Buttons/UI-ScrollBar-ScrollUpButton-Down")
            upButton:SetHighlightTexture("Interface/Buttons/UI-ScrollBar-ScrollUpButton-Highlight")

            upButton:SetScript("OnClick", function()
                local temp = items[i].channelName
                items[i].channelName = items[i-1].channelName
                items[i-1].channelName = temp
                container:UpdateOrder()
            end)
        end

        -- 下移按钮
        if i < #items then
            local downButton = CreateFrame("Button", nil, item)
            downButton:SetSize(16, 16)
            downButton:SetPoint("RIGHT", item, "RIGHT", 0, 0)
            downButton:SetNormalTexture("Interface/Buttons/UI-ScrollBar-ScrollDownButton-Up")
            downButton:SetPushedTexture("Interface/Buttons/UI-ScrollBar-ScrollDownButton-Down")
            downButton:SetHighlightTexture("Interface/Buttons/UI-ScrollBar-ScrollDownButton-Highlight")

            downButton:SetScript("OnClick", function()
                local temp = items[i].channelName
                items[i].channelName = items[i+1].channelName
                items[i+1].channelName = temp
                container:UpdateOrder()
            end)
        end
    end

    return container
end

-- 优化布局，按功能分区
-- 创建系统界面选项的设置面板
local function CreateSettingsPanel()
    local panel = CreateFrame("Frame", "ChatManagerSettingsPanel", UIParent)
    panel.name = "聊天频道管理"
    
    -- 添加标题背景
    local titleBg = panel:CreateTexture(nil, "BACKGROUND")
    titleBg:SetPoint("TOPLEFT", 0, 0)
    titleBg:SetPoint("TOPRIGHT", 0, 0)
    titleBg:SetHeight(50)
    titleBg:SetColorTexture(0.1, 0.1, 0.1, 0.6)
    
    local title = panel:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    title:SetPoint("TOPLEFT", 16, -16)
    title:SetText("聊天频道管理 配置")
    
    -- 优化复选框创建函数
    local function CreateCheckbox(name, label, parent, point, relativePoint, x, y, setting, tooltip)
        local checkbox = CreateFrame("CheckButton", "ChatManager" .. name, parent, "InterfaceOptionsCheckButtonTemplate")
        checkbox:SetPoint(point, relativePoint, x, y)
        checkbox.Text:SetText(label)
        checkbox.Text:SetTextColor(0.9, 0.9, 0.9)
        checkbox:SetChecked(ChatManagerDB[setting])
        
        -- 添加鼠标悬停效果
        checkbox:HookScript("OnEnter", function(self)
            GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
            GameTooltip:SetText(tooltip, 0.9, 0.9, 0.9)
            GameTooltip:Show()
        end)
        checkbox:HookScript("OnLeave", function(self)
            GameTooltip:Hide()
        end)
        
        checkbox:SetScript("OnClick", function(self)
            ChatManagerDB[setting] = self:GetChecked()
        end)
        
        return checkbox
    end

    -- 创建分区容器函数
    local function CreateSection(title, yOffset)
        local section = CreateFrame("Frame", nil, panel, "BackdropTemplate")
        section:SetSize(360, 100) -- 初始高度，后续可调整
        section:SetPoint("TOPLEFT", panel, "TOPLEFT", 16, yOffset)
        
        section:SetBackdrop({
            bgFile = "Interface/Tooltips/UI-Tooltip-Background",
            edgeFile = "Interface/Tooltips/UI-Tooltip-Border",
            tile = true,
            tileSize = 16,
            edgeSize = 16,
            insets = {left = 4, right = 4, top = 4, bottom = 4}
        })
        section:SetBackdropColor(0, 0, 0, 0.7)
        section:SetBackdropBorderColor(0.5, 0.5, 0.5, 1)
        
        -- 添加标题
        local headerBg = section:CreateTexture(nil, "BACKGROUND")
        headerBg:SetPoint("TOPLEFT", 5, -5)
        headerBg:SetPoint("TOPRIGHT", -5, -5)
        headerBg:SetHeight(24)
        headerBg:SetColorTexture(0.2, 0.2, 0.2, 0.9)
        
        local header = section:CreateFontString(nil, "OVERLAY", "GameFontNormalMed2")
        header:SetPoint("TOPLEFT", headerBg, "TOPLEFT", 8, 0)
        header:SetPoint("BOTTOMRIGHT", headerBg, "BOTTOMRIGHT", -8, 0)
        header:SetJustifyH("LEFT")
        header:SetText(title)
        header:SetTextColor(1, 0.82, 0)
        
        return section, 30 -- 返回容器和内容起始Y偏移
    end
    
    -- 1. 频道基本设置区域
    local basicSection, basicY = CreateSection("基本设置", -60)
    basicSection:SetHeight(100)
    
    CreateCheckbox("AutoJoin", "自动加入频道", basicSection, "TOPLEFT", basicSection, 10, -basicY, "autoJoinChannels", "自动加入常用频道")
    CreateCheckbox("SwapChannels", "自动调整频道顺序", basicSection, "TOPLEFT", basicSection, 10, -(basicY+25), "swapChannels", "自动根据设置调整频道顺序")
    CreateCheckbox("ReplaceChannelName", "替换大脚世界频道名称", basicSection, "TOPLEFT", basicSection, 10, -(basicY+50), "replaceChannelName", "将大脚世界频道名称替换为自定义缩写")
    
    -- 2. 世界频道缩写设置区域
    local abbrevSection, abbrevY = CreateSection("世界频道缩写", -170)
    abbrevSection:SetHeight(80)
    
    local abbrevLabel = abbrevSection:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    abbrevLabel:SetPoint("TOPLEFT", abbrevSection, 10, -abbrevY)
    abbrevLabel:SetText("世界频道缩写:")
    abbrevLabel:SetTextColor(0.9, 0.9, 0.9)
    
    -- 优化输入框样式
    local abbrevInput = CreateFrame("EditBox", "ChatManagerWorldAbbrevInput", abbrevSection, "InputBoxTemplate")
    abbrevInput:SetPoint("LEFT", abbrevLabel, "RIGHT", 10, 0)
    abbrevInput:SetSize(100, 20)
    abbrevInput:SetAutoFocus(false)
    abbrevInput:SetMaxLetters(12)
    abbrevInput:SetFontObject(GameFontHighlight)
    
    -- 设置默认值
    if ChatManagerDB.channelNames and ChatManagerDB.channelNames["大脚世界频道"] then
        abbrevInput:SetText(ChatManagerDB.channelNames["大脚世界频道"])
    else
        abbrevInput:SetText("世界")
    end
    
    -- 添加输入框交互效果
    abbrevInput:SetScript("OnEditFocusGained", function(self)
        self:HighlightText()
        self:SetBackdropBorderColor(0.5, 0.5, 1, 1)
    end)
    
    abbrevInput:SetScript("OnEditFocusLost", function(self)
        self:HighlightText(0, 0)
        self:SetBackdropBorderColor(0.6, 0.6, 0.6, 1)
        -- 保存更改
        ChatManagerDB.channelNames["大脚世界频道"] = self:GetText()
        RenameBigfootChannel()
    end)
    
    -- 添加输入框说明
    local abbrevDesc = abbrevSection:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
    abbrevDesc:SetPoint("TOPLEFT", abbrevLabel, "BOTTOMLEFT", 0, -8)
    abbrevDesc:SetText("在聊天窗口中显示的简短名称")
    abbrevDesc:SetTextColor(0.7, 0.7, 0.7)
    
    -- 3. 头像伤害数字设置区域
    local combatSection, combatY = CreateSection("头像伤害数字设置", -260)
    combatSection:SetHeight(80)
    
    -- 确保combatText设置存在
    if not ChatManagerDB.combatText then
        ChatManagerDB.combatText = {
            hidePlayerFrame = false,
            hidePetFrame = true
        }
    end
    
    -- 玩家头像伤害数字设置
    local playerFrameCheckbox = CreateFrame("CheckButton", "ChatManagerHidePlayerCombatText", combatSection, "InterfaceOptionsCheckButtonTemplate")
    playerFrameCheckbox:SetPoint("TOPLEFT", combatSection, 10, -combatY)
    playerFrameCheckbox.Text:SetText("隐藏玩家头像伤害数字")
    playerFrameCheckbox.Text:SetTextColor(0.9, 0.9, 0.9)
    playerFrameCheckbox:SetChecked(ChatManagerDB.combatText.hidePlayerFrame)
    playerFrameCheckbox:SetScript("OnClick", function(self)
        ChatManagerDB.combatText.hidePlayerFrame = self:GetChecked()
        if ChatManagerDB.combatText.hidePlayerFrame then
            PlayerFrame:UnregisterEvent("UNIT_COMBAT")
        else
            PlayerFrame:RegisterEvent("UNIT_COMBAT")
        end
    end)
    
    -- 宠物头像伤害数字设置
    local petFrameCheckbox = CreateFrame("CheckButton", "ChatManagerHidePetCombatText", combatSection, "InterfaceOptionsCheckButtonTemplate")
    petFrameCheckbox:SetPoint("TOPLEFT", combatSection, 10, -(combatY+25))
    petFrameCheckbox.Text:SetText("隐藏宠物头像伤害数字")
    petFrameCheckbox.Text:SetTextColor(0.9, 0.9, 0.9)
    petFrameCheckbox:SetChecked(ChatManagerDB.combatText.hidePetFrame)
    petFrameCheckbox:SetScript("OnClick", function(self)
        ChatManagerDB.combatText.hidePetFrame = self:GetChecked()
        if ChatManagerDB.combatText.hidePetFrame then
            PetFrame:UnregisterEvent("UNIT_COMBAT")
        else
            PetFrame:RegisterEvent("UNIT_COMBAT")
        end
    end)
    
    -- 4. 频道排序设置区域
    local channelListSection = CreateDraggableChannelList(panel)
    channelListSection:SetPoint("TOPLEFT", panel, "TOPLEFT", 16, -350)
    
    InterfaceOptions_AddCategory(panel)
    return panel
end

--------------------------------------------------
-- 初始化函数
--------------------------------------------------
local function Initialize()
    CreateSettingsPanel()
    
    local eventFrame = CreateFrame("Frame")
    eventFrame:RegisterEvent("PLAYER_ENTERING_WORLD")
    eventFrame:RegisterEvent("CHANNEL_UI_UPDATE")
    eventFrame:SetScript("OnEvent", function(self, event)
        if event == "PLAYER_ENTERING_WORLD" then
            JoinChannels()
            SwapChannels()
            RenameBigfootChannel()
            
            -- 应用玩家和宠物头像伤害数字设置
            if ChatManagerDB.combatText then
                if ChatManagerDB.combatText.hidePlayerFrame then
                    PlayerFrame:UnregisterEvent("UNIT_COMBAT")
                else
                    PlayerFrame:RegisterEvent("UNIT_COMBAT")
                end
                
                if ChatManagerDB.combatText.hidePetFrame then
                    PetFrame:UnregisterEvent("UNIT_COMBAT")
                else
                    PetFrame:RegisterEvent("UNIT_COMBAT")
                end
            end
        elseif event == "CHANNEL_UI_UPDATE" then
            SwapChannels()
        end
    end)
end

Initialize()